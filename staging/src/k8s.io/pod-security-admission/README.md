# Pod Security Admission

<!-- TODO: Placeholder README. Update with more detail and repo contents once initial implementation is in place. -->

The **Pod Security Standards** are a set of best-practice profiles for running pods securely.

This repository contains the codified profile definitions, the implementation for the
**PodSecurity** admission controller (library and webhook) that enforces the use of the standards,
and testing resources for validating enforcement of the standards.

See https://github.com/kubernetes/enhancements/tree/master/keps/sig-auth/2579-psp-replacement for more details.

## Community, discussion, contribution, and support

The Pod Security Standards are a sub-project of [SIG-Auth](https://github.com/kubernetes/community/tree/master/sig-auth).

You can reach the maintainers of this project at:

- Slack: [#sig-auth](https://kubernetes.slack.com/messages/sig-auth)
- Mailing List: [kubernetes-sig-auth](https://groups.google.com/forum/#!forum/kubernetes-sig-auth)

Learn how to engage with the Kubernetes community on the [community page](http://kubernetes.io/community/).

### Code of conduct

Participation in the Kubernetes community is governed by the [Kubernetes Code of Conduct](code-of-conduct.md).

