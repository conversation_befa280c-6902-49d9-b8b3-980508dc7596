apiVersion: v1
kind: Pod
metadata:
  name: windowshostprocess1
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      windowsOptions:
        hostProcess: true
  hostNetwork: true
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      windowsOptions:
        hostProcess: true
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
    windowsOptions: {}
