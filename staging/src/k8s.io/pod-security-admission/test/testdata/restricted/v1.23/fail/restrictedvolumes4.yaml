apiVersion: v1
kind: Pod
metadata:
  name: restrictedvolumes4
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
  volumes:
  - iscsi:
      iqn: iqn.2001-04.com.example:storage.kube.sys1.xyz
      lun: 0
      targetPortal: test
    name: volume1
