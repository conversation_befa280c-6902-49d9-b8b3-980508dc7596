apiVersion: v1
kind: Pod
metadata:
  name: runasnonroot1
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      runAsNonRoot: true
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      runAsNonRoot: true
  securityContext:
    seccompProfile:
      type: RuntimeDefault
