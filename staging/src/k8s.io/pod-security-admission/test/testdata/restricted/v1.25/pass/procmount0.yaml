apiVersion: v1
kind: Pod
metadata:
  name: procmount0
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      procMount: Default
  hostUsers: false
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      procMount: Default
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
