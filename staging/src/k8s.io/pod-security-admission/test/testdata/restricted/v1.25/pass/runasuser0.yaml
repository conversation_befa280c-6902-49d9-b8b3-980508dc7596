apiVersion: v1
kind: Pod
metadata:
  name: runasuser0
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      runAsUser: 1000
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      runAsUser: 1000
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    seccompProfile:
      type: RuntimeDefault
