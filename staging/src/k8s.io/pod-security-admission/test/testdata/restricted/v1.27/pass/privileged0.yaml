apiVersion: v1
kind: Pod
metadata:
  name: privileged0
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      privileged: false
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      privileged: false
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
