apiVersion: v1
kind: Pod
metadata:
  name: hostports0
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    ports:
    - containerPort: 12345
      hostPort: 12345
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
