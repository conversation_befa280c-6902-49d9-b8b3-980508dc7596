apiVersion: v1
kind: Pod
metadata:
  name: selinuxoptions2
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      seLinuxOptions: {}
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      seLinuxOptions:
        type: somevalue
  securityContext:
    runAsNonRoot: true
    seLinuxOptions: {}
    seccompProfile:
      type: RuntimeDefault
