apiVersion: v1
kind: Pod
metadata:
  name: selinuxoptions1
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      seLinuxOptions:
        level: somevalue
        type: container_init_t
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      seLinuxOptions:
        type: container_kvm_t
  securityContext:
    runAsNonRoot: true
    seLinuxOptions:
      type: container_t
    seccompProfile:
      type: RuntimeDefault
