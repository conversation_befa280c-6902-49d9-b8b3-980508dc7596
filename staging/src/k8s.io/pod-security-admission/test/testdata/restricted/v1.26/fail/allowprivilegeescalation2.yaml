apiVersion: v1
kind: Pod
metadata:
  name: allowprivilegeescalation2
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      capabilities:
        drop:
        - ALL
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
