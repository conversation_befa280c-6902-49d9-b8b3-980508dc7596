apiVersion: v1
kind: Pod
metadata:
  name: base_linux
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
  os:
    name: linux
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
