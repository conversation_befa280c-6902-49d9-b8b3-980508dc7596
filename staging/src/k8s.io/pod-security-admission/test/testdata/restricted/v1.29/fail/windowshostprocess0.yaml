apiVersion: v1
kind: Pod
metadata:
  name: windowshostprocess0
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      windowsOptions: {}
  hostNetwork: true
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      windowsOptions: {}
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
    windowsOptions:
      hostProcess: true
