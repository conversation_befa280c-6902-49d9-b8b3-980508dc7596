apiVersion: v1
kind: Pod
metadata:
  name: runasuser2
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      runAsUser: 0
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
