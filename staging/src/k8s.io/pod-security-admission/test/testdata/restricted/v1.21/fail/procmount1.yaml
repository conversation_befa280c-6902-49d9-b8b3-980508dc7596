apiVersion: v1
kind: Pod
metadata:
  name: procmount1
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
  hostUsers: false
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      procMount: Unmasked
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
