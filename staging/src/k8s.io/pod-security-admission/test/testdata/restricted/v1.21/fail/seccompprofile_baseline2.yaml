apiVersion: v1
kind: Pod
metadata:
  name: seccompprofile_baseline2
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      seccompProfile:
        type: Unconfined
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
