apiVersion: v1
kind: Pod
metadata:
  name: selinuxoptions4
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
      seLinuxOptions: {}
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      seLinuxOptions: {}
  securityContext:
    runAsNonRoot: true
    seLinuxOptions:
      role: somevalue
    seccompProfile:
      type: RuntimeDefault
