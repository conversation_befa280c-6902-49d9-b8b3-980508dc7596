apiVersion: v1
kind: Pod
metadata:
  name: restrictedvolumes17
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
  volumes:
  - name: volume1
    scaleIO:
      gateway: localhost
      secretRef: null
      system: test
      volumeName: test
