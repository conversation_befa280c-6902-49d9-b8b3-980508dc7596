apiVersion: v1
kind: Pod
metadata:
  name: windowshostprocess1
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
      windowsOptions:
        hostProcess: true
  hostNetwork: true
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
      windowsOptions:
        hostProcess: true
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
    windowsOptions: {}
