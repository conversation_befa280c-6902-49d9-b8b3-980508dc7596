apiVersion: v1
kind: Pod
metadata:
  name: hostnamespaces0
spec:
  containers:
  - image: registry.k8s.io/pause
    name: container1
    securityContext:
      allowPrivilegeEscalation: false
  hostIPC: true
  initContainers:
  - image: registry.k8s.io/pause
    name: initcontainer1
    securityContext:
      allowPrivilegeEscalation: false
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
