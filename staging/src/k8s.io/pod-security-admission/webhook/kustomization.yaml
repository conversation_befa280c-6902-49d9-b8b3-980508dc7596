# include the manifests
bases:
- ./manifests

# generate the secret
# this depends on pki files, which can be created (or regenerated) with `make certs`
secretGenerator:
- name: pod-security-webhook
  namespace: pod-security-webhook
  type: kubernetes.io/tls
  options:
    disableNameSuffixHash: true
  files:
  - pki/ca.crt
  - pki/tls.crt
  - pki/tls.key

# inject the CA into the validating webhook
replacements:
- source:
    kind: Secret
    name: pod-security-webhook
    namespace: pod-security-webhook
    fieldPath: data.ca\.crt
  targets:
  - select:
      kind: ValidatingWebhookConfiguration
      name: pod-security-webhook.kubernetes.io
    fieldPaths:
     - webhooks.0.clientConfig.caBundle
     - webhooks.1.clientConfig.caBundle
    options:
      create: true
