{"kind": "CustomResourceDefinition", "apiVersion": "apiextensions.k8s.io/v1", "metadata": {"name": "nameValue", "generateName": "generateNameValue", "namespace": "namespaceValue", "selfLink": "selfLinkValue", "uid": "uidValue", "resourceVersion": "resourceVersionValue", "generation": 7, "creationTimestamp": "2008-01-01T01:01:01Z", "deletionTimestamp": "2009-01-01T01:01:01Z", "deletionGracePeriodSeconds": 10, "labels": {"labelsKey": "labelsValue"}, "annotations": {"annotationsKey": "annotationsValue"}, "ownerReferences": [{"apiVersion": "apiVersionValue", "kind": "kindValue", "name": "nameValue", "uid": "uidValue", "controller": true, "blockOwnerDeletion": true}], "finalizers": ["finalizersV<PERSON>ue"], "managedFields": [{"manager": "<PERSON><PERSON><PERSON><PERSON>", "operation": "operationValue", "apiVersion": "apiVersionValue", "time": "2004-01-01T01:01:01Z", "fieldsType": "fieldsTypeValue", "fieldsV1": {}, "subresource": "subresourceValue"}]}, "spec": {"group": "groupValue", "names": {"plural": "pluralValue", "singular": "singularValue", "shortNames": ["shortNamesValue"], "kind": "kindValue", "listKind": "listKindValue", "categories": ["categoriesValue"]}, "scope": "scopeValue", "versions": [{"name": "nameValue", "served": true, "storage": true, "deprecated": true, "deprecationWarning": "deprecationWarningValue", "schema": {"openAPIV3Schema": {"id": "idValue", "$schema": "$schemaValue", "$ref": "$refValue", "description": "descriptionValue", "type": "typeValue", "format": "formatValue", "title": "titleValue", "default": "defaultValue", "maximum": 9.5, "exclusiveMaximum": true, "minimum": 11.5, "exclusiveMinimum": true, "maxLength": 13, "minLength": 14, "pattern": "patternValue", "maxItems": 16, "minItems": 17, "uniqueItems": true, "multipleOf": 19.5, "enum": ["enumValue"], "maxProperties": 21, "minProperties": 22, "required": ["requiredValue"], "items": [{}], "allOf": [{}], "oneOf": [{}], "anyOf": [{}], "properties": {"propertiesKey": {}}, "additionalProperties": true, "patternProperties": {"patternPropertiesKey": {}}, "dependencies": {"dependenciesKey": ["<no json tag> PropertyValue"]}, "additionalItems": true, "definitions": {"definitionsKey": {}}, "externalDocs": {"description": "descriptionValue", "url": "urlValue"}, "example": "exampleValue", "nullable": true, "x-kubernetes-preserve-unknown-fields": true, "x-kubernetes-embedded-resource": true, "x-kubernetes-int-or-string": true, "x-kubernetes-list-map-keys": ["x-kubernetes-list-map-keysValue"], "x-kubernetes-list-type": "x-kubernetes-list-typeValue", "x-kubernetes-map-type": "x-kubernetes-map-typeValue", "x-kubernetes-validations": [{"rule": "ruleValue", "message": "messageValue", "messageExpression": "messageExpressionValue", "reason": "reasonValue", "fieldPath": "fieldPathValue", "optionalOldSelf": true}]}}, "subresources": {"status": {}, "scale": {"specReplicasPath": "specReplicasPathValue", "statusReplicasPath": "statusReplicasPathValue", "labelSelectorPath": "labelSelectorPathValue"}}, "additionalPrinterColumns": [{"name": "nameValue", "type": "typeValue", "format": "formatValue", "description": "descriptionValue", "priority": 5, "jsonPath": "jsonPathValue"}], "selectableFields": [{"jsonPath": "jsonPathValue"}]}], "conversion": {"strategy": "strategyValue", "webhook": {"clientConfig": {"url": "urlValue", "service": {"namespace": "namespaceValue", "name": "nameValue", "path": "pathValue", "port": 4}, "caBundle": "Ag=="}, "conversionReviewVersions": ["conversionReviewVersionsValue"]}}, "preserveUnknownFields": true}, "status": {"conditions": [{"type": "typeValue", "status": "statusValue", "lastTransitionTime": "2003-01-01T01:01:01Z", "reason": "reasonValue", "message": "messageValue"}], "acceptedNames": {"plural": "pluralValue", "singular": "singularValue", "shortNames": ["shortNamesValue"], "kind": "kindValue", "listKind": "listKindValue", "categories": ["categoriesValue"]}, "storedVersions": ["storedVersionsValue"]}}