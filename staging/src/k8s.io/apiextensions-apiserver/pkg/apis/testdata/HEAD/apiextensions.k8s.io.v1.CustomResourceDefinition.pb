k8s 
3
apiextensions.k8s.io/v1CustomResourceDefinition�
�
	nameValuegenerateNameValuenamespaceValue"
selfLinkValue*uidValue2resourceVersionValue8B͡� Jͫ�� P
Z
	labelsKeylabelsValueb"
annotationsKeyannotationsValuej5
	kindValue	nameValue"uidValue*apiVersionValue08rfinalizersValue�b
managerValueoperationValueapiVersionValue"���� 2fieldsTypeValue:
{}BsubresourceValue�	

groupValueX
pluralValue
singularValueshortNamesValue"	kindValue*
listKindValue2categoriesValue"
scopeValue:�
	nameValue"�
�
idValue$schemaValue	$refValue"descriptionValue*	typeValue2formatValue:
titleValueB
"defaultValue"I      #@PY      '@`h
pzpatternValue����     �3@�
"enumValue"���
requiredValue� 
  " * 2 : P ` z � � � � �
  " * 2 : P ` z � � � � �
  " * 2 : P ` z � � � � �
  " * 2 : P ` z � � � � �/

propertiesKey
  " * 2 : P ` z � � � � ��6
patternPropertiesKey
  " * 2 : P ` z � � � � �0
dependenciesKey<no json tag> PropertyValue��0
definitionsKey
  " * 2 : P ` z � � � � �
descriptionValueurlValue�
"exampleValue"�����x-kubernetes-list-map-keysValue�x-kubernetes-list-typeValue�x-kubernetes-map-typeValue�P
	ruleValuemessageValuemessageExpressionValue"reasonValue*fieldPathValue0*L
 H
specReplicasPathValuestatusReplicasPathValuelabelSelectorPathValue2F
	nameValue	typeValueformatValue"descriptionValue(2
jsonPathValue8BdeprecationWarningValueJ

jsonPathValueJi

strategyValueX7
(
namespaceValue	nameValue	pathValue urlValueconversionReviewVersionsValueP�
=
	typeValuestatusValue���� "reasonValue*messageValueX
pluralValue
singularValueshortNamesValue"	kindValue*
listKindValue2categoriesValuestoredVersionsValue " 