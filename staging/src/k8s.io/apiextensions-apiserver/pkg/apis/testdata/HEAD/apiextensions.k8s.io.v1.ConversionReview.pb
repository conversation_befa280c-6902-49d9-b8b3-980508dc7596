k8s 
+
apiextensions.k8s.io/v1ConversionReview�
�
uidValuedesiredAPIVersionValued
b{"apiVersion":"example.com/v1","kind":"CustomType","spec":{"replicas":1},"status":{"available":1}}�
uidValued
b{"apiVersion":"example.com/v1","kind":"CustomType","spec":{"replicas":1},"status":{"available":1}}�
6

selfLinkValueresourceVersionValue
continueValue statusValuemessageValue"reasonValue*W
	nameValue
groupValue	kindValue"'
reasonValuemessageValue
fieldValue(2uidValue0 " 