/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1/generated.proto

package v1

import (
	encoding_binary "encoding/binary"
	fmt "fmt"

	io "io"

	proto "github.com/gogo/protobuf/proto"
	github_com_gogo_protobuf_sortkeys "github.com/gogo/protobuf/sortkeys"
	runtime "k8s.io/apimachinery/pkg/runtime"

	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strings "strings"

	k8s_io_apimachinery_pkg_types "k8s.io/apimachinery/pkg/types"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

func (m *ConversionRequest) Reset()      { *m = ConversionRequest{} }
func (*ConversionRequest) ProtoMessage() {}
func (*ConversionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{0}
}
func (m *ConversionRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ConversionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *ConversionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConversionRequest.Merge(m, src)
}
func (m *ConversionRequest) XXX_Size() int {
	return m.Size()
}
func (m *ConversionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ConversionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ConversionRequest proto.InternalMessageInfo

func (m *ConversionResponse) Reset()      { *m = ConversionResponse{} }
func (*ConversionResponse) ProtoMessage() {}
func (*ConversionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{1}
}
func (m *ConversionResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ConversionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *ConversionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConversionResponse.Merge(m, src)
}
func (m *ConversionResponse) XXX_Size() int {
	return m.Size()
}
func (m *ConversionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ConversionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ConversionResponse proto.InternalMessageInfo

func (m *ConversionReview) Reset()      { *m = ConversionReview{} }
func (*ConversionReview) ProtoMessage() {}
func (*ConversionReview) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{2}
}
func (m *ConversionReview) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ConversionReview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *ConversionReview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConversionReview.Merge(m, src)
}
func (m *ConversionReview) XXX_Size() int {
	return m.Size()
}
func (m *ConversionReview) XXX_DiscardUnknown() {
	xxx_messageInfo_ConversionReview.DiscardUnknown(m)
}

var xxx_messageInfo_ConversionReview proto.InternalMessageInfo

func (m *CustomResourceColumnDefinition) Reset()      { *m = CustomResourceColumnDefinition{} }
func (*CustomResourceColumnDefinition) ProtoMessage() {}
func (*CustomResourceColumnDefinition) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{3}
}
func (m *CustomResourceColumnDefinition) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CustomResourceColumnDefinition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *CustomResourceColumnDefinition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomResourceColumnDefinition.Merge(m, src)
}
func (m *CustomResourceColumnDefinition) XXX_Size() int {
	return m.Size()
}
func (m *CustomResourceColumnDefinition) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomResourceColumnDefinition.DiscardUnknown(m)
}

var xxx_messageInfo_CustomResourceColumnDefinition proto.InternalMessageInfo

func (m *CustomResourceConversion) Reset()      { *m = CustomResourceConversion{} }
func (*CustomResourceConversion) ProtoMessage() {}
func (*CustomResourceConversion) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{4}
}
func (m *CustomResourceConversion) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CustomResourceConversion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *CustomResourceConversion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomResourceConversion.Merge(m, src)
}
func (m *CustomResourceConversion) XXX_Size() int {
	return m.Size()
}
func (m *CustomResourceConversion) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomResourceConversion.DiscardUnknown(m)
}

var xxx_messageInfo_CustomResourceConversion proto.InternalMessageInfo

func (m *CustomResourceDefinition) Reset()      { *m = CustomResourceDefinition{} }
func (*CustomResourceDefinition) ProtoMessage() {}
func (*CustomResourceDefinition) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{5}
}
func (m *CustomResourceDefinition) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CustomResourceDefinition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *CustomResourceDefinition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomResourceDefinition.Merge(m, src)
}
func (m *CustomResourceDefinition) XXX_Size() int {
	return m.Size()
}
func (m *CustomResourceDefinition) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomResourceDefinition.DiscardUnknown(m)
}

var xxx_messageInfo_CustomResourceDefinition proto.InternalMessageInfo

func (m *CustomResourceDefinitionCondition) Reset()      { *m = CustomResourceDefinitionCondition{} }
func (*CustomResourceDefinitionCondition) ProtoMessage() {}
func (*CustomResourceDefinitionCondition) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{6}
}
func (m *CustomResourceDefinitionCondition) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CustomResourceDefinitionCondition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *CustomResourceDefinitionCondition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomResourceDefinitionCondition.Merge(m, src)
}
func (m *CustomResourceDefinitionCondition) XXX_Size() int {
	return m.Size()
}
func (m *CustomResourceDefinitionCondition) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomResourceDefinitionCondition.DiscardUnknown(m)
}

var xxx_messageInfo_CustomResourceDefinitionCondition proto.InternalMessageInfo

func (m *CustomResourceDefinitionList) Reset()      { *m = CustomResourceDefinitionList{} }
func (*CustomResourceDefinitionList) ProtoMessage() {}
func (*CustomResourceDefinitionList) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{7}
}
func (m *CustomResourceDefinitionList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CustomResourceDefinitionList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *CustomResourceDefinitionList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomResourceDefinitionList.Merge(m, src)
}
func (m *CustomResourceDefinitionList) XXX_Size() int {
	return m.Size()
}
func (m *CustomResourceDefinitionList) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomResourceDefinitionList.DiscardUnknown(m)
}

var xxx_messageInfo_CustomResourceDefinitionList proto.InternalMessageInfo

func (m *CustomResourceDefinitionNames) Reset()      { *m = CustomResourceDefinitionNames{} }
func (*CustomResourceDefinitionNames) ProtoMessage() {}
func (*CustomResourceDefinitionNames) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{8}
}
func (m *CustomResourceDefinitionNames) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CustomResourceDefinitionNames) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *CustomResourceDefinitionNames) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomResourceDefinitionNames.Merge(m, src)
}
func (m *CustomResourceDefinitionNames) XXX_Size() int {
	return m.Size()
}
func (m *CustomResourceDefinitionNames) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomResourceDefinitionNames.DiscardUnknown(m)
}

var xxx_messageInfo_CustomResourceDefinitionNames proto.InternalMessageInfo

func (m *CustomResourceDefinitionSpec) Reset()      { *m = CustomResourceDefinitionSpec{} }
func (*CustomResourceDefinitionSpec) ProtoMessage() {}
func (*CustomResourceDefinitionSpec) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{9}
}
func (m *CustomResourceDefinitionSpec) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CustomResourceDefinitionSpec) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *CustomResourceDefinitionSpec) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomResourceDefinitionSpec.Merge(m, src)
}
func (m *CustomResourceDefinitionSpec) XXX_Size() int {
	return m.Size()
}
func (m *CustomResourceDefinitionSpec) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomResourceDefinitionSpec.DiscardUnknown(m)
}

var xxx_messageInfo_CustomResourceDefinitionSpec proto.InternalMessageInfo

func (m *CustomResourceDefinitionStatus) Reset()      { *m = CustomResourceDefinitionStatus{} }
func (*CustomResourceDefinitionStatus) ProtoMessage() {}
func (*CustomResourceDefinitionStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{10}
}
func (m *CustomResourceDefinitionStatus) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CustomResourceDefinitionStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *CustomResourceDefinitionStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomResourceDefinitionStatus.Merge(m, src)
}
func (m *CustomResourceDefinitionStatus) XXX_Size() int {
	return m.Size()
}
func (m *CustomResourceDefinitionStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomResourceDefinitionStatus.DiscardUnknown(m)
}

var xxx_messageInfo_CustomResourceDefinitionStatus proto.InternalMessageInfo

func (m *CustomResourceDefinitionVersion) Reset()      { *m = CustomResourceDefinitionVersion{} }
func (*CustomResourceDefinitionVersion) ProtoMessage() {}
func (*CustomResourceDefinitionVersion) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{11}
}
func (m *CustomResourceDefinitionVersion) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CustomResourceDefinitionVersion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *CustomResourceDefinitionVersion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomResourceDefinitionVersion.Merge(m, src)
}
func (m *CustomResourceDefinitionVersion) XXX_Size() int {
	return m.Size()
}
func (m *CustomResourceDefinitionVersion) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomResourceDefinitionVersion.DiscardUnknown(m)
}

var xxx_messageInfo_CustomResourceDefinitionVersion proto.InternalMessageInfo

func (m *CustomResourceSubresourceScale) Reset()      { *m = CustomResourceSubresourceScale{} }
func (*CustomResourceSubresourceScale) ProtoMessage() {}
func (*CustomResourceSubresourceScale) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{12}
}
func (m *CustomResourceSubresourceScale) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CustomResourceSubresourceScale) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *CustomResourceSubresourceScale) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomResourceSubresourceScale.Merge(m, src)
}
func (m *CustomResourceSubresourceScale) XXX_Size() int {
	return m.Size()
}
func (m *CustomResourceSubresourceScale) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomResourceSubresourceScale.DiscardUnknown(m)
}

var xxx_messageInfo_CustomResourceSubresourceScale proto.InternalMessageInfo

func (m *CustomResourceSubresourceStatus) Reset()      { *m = CustomResourceSubresourceStatus{} }
func (*CustomResourceSubresourceStatus) ProtoMessage() {}
func (*CustomResourceSubresourceStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{13}
}
func (m *CustomResourceSubresourceStatus) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CustomResourceSubresourceStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *CustomResourceSubresourceStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomResourceSubresourceStatus.Merge(m, src)
}
func (m *CustomResourceSubresourceStatus) XXX_Size() int {
	return m.Size()
}
func (m *CustomResourceSubresourceStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomResourceSubresourceStatus.DiscardUnknown(m)
}

var xxx_messageInfo_CustomResourceSubresourceStatus proto.InternalMessageInfo

func (m *CustomResourceSubresources) Reset()      { *m = CustomResourceSubresources{} }
func (*CustomResourceSubresources) ProtoMessage() {}
func (*CustomResourceSubresources) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{14}
}
func (m *CustomResourceSubresources) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CustomResourceSubresources) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *CustomResourceSubresources) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomResourceSubresources.Merge(m, src)
}
func (m *CustomResourceSubresources) XXX_Size() int {
	return m.Size()
}
func (m *CustomResourceSubresources) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomResourceSubresources.DiscardUnknown(m)
}

var xxx_messageInfo_CustomResourceSubresources proto.InternalMessageInfo

func (m *CustomResourceValidation) Reset()      { *m = CustomResourceValidation{} }
func (*CustomResourceValidation) ProtoMessage() {}
func (*CustomResourceValidation) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{15}
}
func (m *CustomResourceValidation) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CustomResourceValidation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *CustomResourceValidation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomResourceValidation.Merge(m, src)
}
func (m *CustomResourceValidation) XXX_Size() int {
	return m.Size()
}
func (m *CustomResourceValidation) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomResourceValidation.DiscardUnknown(m)
}

var xxx_messageInfo_CustomResourceValidation proto.InternalMessageInfo

func (m *ExternalDocumentation) Reset()      { *m = ExternalDocumentation{} }
func (*ExternalDocumentation) ProtoMessage() {}
func (*ExternalDocumentation) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{16}
}
func (m *ExternalDocumentation) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExternalDocumentation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *ExternalDocumentation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExternalDocumentation.Merge(m, src)
}
func (m *ExternalDocumentation) XXX_Size() int {
	return m.Size()
}
func (m *ExternalDocumentation) XXX_DiscardUnknown() {
	xxx_messageInfo_ExternalDocumentation.DiscardUnknown(m)
}

var xxx_messageInfo_ExternalDocumentation proto.InternalMessageInfo

func (m *JSON) Reset()      { *m = JSON{} }
func (*JSON) ProtoMessage() {}
func (*JSON) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{17}
}
func (m *JSON) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *JSON) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *JSON) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JSON.Merge(m, src)
}
func (m *JSON) XXX_Size() int {
	return m.Size()
}
func (m *JSON) XXX_DiscardUnknown() {
	xxx_messageInfo_JSON.DiscardUnknown(m)
}

var xxx_messageInfo_JSON proto.InternalMessageInfo

func (m *JSONSchemaProps) Reset()      { *m = JSONSchemaProps{} }
func (*JSONSchemaProps) ProtoMessage() {}
func (*JSONSchemaProps) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{18}
}
func (m *JSONSchemaProps) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *JSONSchemaProps) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *JSONSchemaProps) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JSONSchemaProps.Merge(m, src)
}
func (m *JSONSchemaProps) XXX_Size() int {
	return m.Size()
}
func (m *JSONSchemaProps) XXX_DiscardUnknown() {
	xxx_messageInfo_JSONSchemaProps.DiscardUnknown(m)
}

var xxx_messageInfo_JSONSchemaProps proto.InternalMessageInfo

func (m *JSONSchemaPropsOrArray) Reset()      { *m = JSONSchemaPropsOrArray{} }
func (*JSONSchemaPropsOrArray) ProtoMessage() {}
func (*JSONSchemaPropsOrArray) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{19}
}
func (m *JSONSchemaPropsOrArray) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *JSONSchemaPropsOrArray) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *JSONSchemaPropsOrArray) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JSONSchemaPropsOrArray.Merge(m, src)
}
func (m *JSONSchemaPropsOrArray) XXX_Size() int {
	return m.Size()
}
func (m *JSONSchemaPropsOrArray) XXX_DiscardUnknown() {
	xxx_messageInfo_JSONSchemaPropsOrArray.DiscardUnknown(m)
}

var xxx_messageInfo_JSONSchemaPropsOrArray proto.InternalMessageInfo

func (m *JSONSchemaPropsOrBool) Reset()      { *m = JSONSchemaPropsOrBool{} }
func (*JSONSchemaPropsOrBool) ProtoMessage() {}
func (*JSONSchemaPropsOrBool) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{20}
}
func (m *JSONSchemaPropsOrBool) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *JSONSchemaPropsOrBool) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *JSONSchemaPropsOrBool) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JSONSchemaPropsOrBool.Merge(m, src)
}
func (m *JSONSchemaPropsOrBool) XXX_Size() int {
	return m.Size()
}
func (m *JSONSchemaPropsOrBool) XXX_DiscardUnknown() {
	xxx_messageInfo_JSONSchemaPropsOrBool.DiscardUnknown(m)
}

var xxx_messageInfo_JSONSchemaPropsOrBool proto.InternalMessageInfo

func (m *JSONSchemaPropsOrStringArray) Reset()      { *m = JSONSchemaPropsOrStringArray{} }
func (*JSONSchemaPropsOrStringArray) ProtoMessage() {}
func (*JSONSchemaPropsOrStringArray) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{21}
}
func (m *JSONSchemaPropsOrStringArray) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *JSONSchemaPropsOrStringArray) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *JSONSchemaPropsOrStringArray) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JSONSchemaPropsOrStringArray.Merge(m, src)
}
func (m *JSONSchemaPropsOrStringArray) XXX_Size() int {
	return m.Size()
}
func (m *JSONSchemaPropsOrStringArray) XXX_DiscardUnknown() {
	xxx_messageInfo_JSONSchemaPropsOrStringArray.DiscardUnknown(m)
}

var xxx_messageInfo_JSONSchemaPropsOrStringArray proto.InternalMessageInfo

func (m *SelectableField) Reset()      { *m = SelectableField{} }
func (*SelectableField) ProtoMessage() {}
func (*SelectableField) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{22}
}
func (m *SelectableField) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SelectableField) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *SelectableField) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectableField.Merge(m, src)
}
func (m *SelectableField) XXX_Size() int {
	return m.Size()
}
func (m *SelectableField) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectableField.DiscardUnknown(m)
}

var xxx_messageInfo_SelectableField proto.InternalMessageInfo

func (m *ServiceReference) Reset()      { *m = ServiceReference{} }
func (*ServiceReference) ProtoMessage() {}
func (*ServiceReference) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{23}
}
func (m *ServiceReference) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ServiceReference) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *ServiceReference) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServiceReference.Merge(m, src)
}
func (m *ServiceReference) XXX_Size() int {
	return m.Size()
}
func (m *ServiceReference) XXX_DiscardUnknown() {
	xxx_messageInfo_ServiceReference.DiscardUnknown(m)
}

var xxx_messageInfo_ServiceReference proto.InternalMessageInfo

func (m *ValidationRule) Reset()      { *m = ValidationRule{} }
func (*ValidationRule) ProtoMessage() {}
func (*ValidationRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{24}
}
func (m *ValidationRule) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ValidationRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *ValidationRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidationRule.Merge(m, src)
}
func (m *ValidationRule) XXX_Size() int {
	return m.Size()
}
func (m *ValidationRule) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidationRule.DiscardUnknown(m)
}

var xxx_messageInfo_ValidationRule proto.InternalMessageInfo

func (m *WebhookClientConfig) Reset()      { *m = WebhookClientConfig{} }
func (*WebhookClientConfig) ProtoMessage() {}
func (*WebhookClientConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{25}
}
func (m *WebhookClientConfig) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WebhookClientConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *WebhookClientConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WebhookClientConfig.Merge(m, src)
}
func (m *WebhookClientConfig) XXX_Size() int {
	return m.Size()
}
func (m *WebhookClientConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_WebhookClientConfig.DiscardUnknown(m)
}

var xxx_messageInfo_WebhookClientConfig proto.InternalMessageInfo

func (m *WebhookConversion) Reset()      { *m = WebhookConversion{} }
func (*WebhookConversion) ProtoMessage() {}
func (*WebhookConversion) Descriptor() ([]byte, []int) {
	return fileDescriptor_c5e101a0235c8c62, []int{26}
}
func (m *WebhookConversion) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WebhookConversion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	b = b[:cap(b)]
	n, err := m.MarshalToSizedBuffer(b)
	if err != nil {
		return nil, err
	}
	return b[:n], nil
}
func (m *WebhookConversion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WebhookConversion.Merge(m, src)
}
func (m *WebhookConversion) XXX_Size() int {
	return m.Size()
}
func (m *WebhookConversion) XXX_DiscardUnknown() {
	xxx_messageInfo_WebhookConversion.DiscardUnknown(m)
}

var xxx_messageInfo_WebhookConversion proto.InternalMessageInfo

func init() {
	proto.RegisterType((*ConversionRequest)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.ConversionRequest")
	proto.RegisterType((*ConversionResponse)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.ConversionResponse")
	proto.RegisterType((*ConversionReview)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.ConversionReview")
	proto.RegisterType((*CustomResourceColumnDefinition)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.CustomResourceColumnDefinition")
	proto.RegisterType((*CustomResourceConversion)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.CustomResourceConversion")
	proto.RegisterType((*CustomResourceDefinition)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.CustomResourceDefinition")
	proto.RegisterType((*CustomResourceDefinitionCondition)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.CustomResourceDefinitionCondition")
	proto.RegisterType((*CustomResourceDefinitionList)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.CustomResourceDefinitionList")
	proto.RegisterType((*CustomResourceDefinitionNames)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.CustomResourceDefinitionNames")
	proto.RegisterType((*CustomResourceDefinitionSpec)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.CustomResourceDefinitionSpec")
	proto.RegisterType((*CustomResourceDefinitionStatus)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.CustomResourceDefinitionStatus")
	proto.RegisterType((*CustomResourceDefinitionVersion)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.CustomResourceDefinitionVersion")
	proto.RegisterType((*CustomResourceSubresourceScale)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.CustomResourceSubresourceScale")
	proto.RegisterType((*CustomResourceSubresourceStatus)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.CustomResourceSubresourceStatus")
	proto.RegisterType((*CustomResourceSubresources)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.CustomResourceSubresources")
	proto.RegisterType((*CustomResourceValidation)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.CustomResourceValidation")
	proto.RegisterType((*ExternalDocumentation)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.ExternalDocumentation")
	proto.RegisterType((*JSON)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.JSON")
	proto.RegisterType((*JSONSchemaProps)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.JSONSchemaProps")
	proto.RegisterMapType((JSONSchemaDefinitions)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.JSONSchemaProps.DefinitionsEntry")
	proto.RegisterMapType((JSONSchemaDependencies)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.JSONSchemaProps.DependenciesEntry")
	proto.RegisterMapType((map[string]JSONSchemaProps)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.JSONSchemaProps.PatternPropertiesEntry")
	proto.RegisterMapType((map[string]JSONSchemaProps)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.JSONSchemaProps.PropertiesEntry")
	proto.RegisterType((*JSONSchemaPropsOrArray)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.JSONSchemaPropsOrArray")
	proto.RegisterType((*JSONSchemaPropsOrBool)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.JSONSchemaPropsOrBool")
	proto.RegisterType((*JSONSchemaPropsOrStringArray)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.JSONSchemaPropsOrStringArray")
	proto.RegisterType((*SelectableField)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.SelectableField")
	proto.RegisterType((*ServiceReference)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.ServiceReference")
	proto.RegisterType((*ValidationRule)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.ValidationRule")
	proto.RegisterType((*WebhookClientConfig)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.WebhookClientConfig")
	proto.RegisterType((*WebhookConversion)(nil), "k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1.WebhookConversion")
}

func init() {
	proto.RegisterFile("k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1/generated.proto", fileDescriptor_c5e101a0235c8c62)
}

var fileDescriptor_c5e101a0235c8c62 = []byte{
	// 3166 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x5a, 0xdb, 0x6f, 0x1b, 0xc7,
	0xd5, 0xf7, 0x52, 0x37, 0x6a, 0x24, 0x59, 0xd2, 0xd8, 0xd2, 0xb7, 0x56, 0x6c, 0x51, 0xa6, 0xbf,
	0xf8, 0x53, 0x12, 0x87, 0x4a, 0xf4, 0x25, 0x8d, 0x9b, 0x5e, 0x02, 0x51, 0x92, 0x13, 0xc5, 0x92,
	0x25, 0x0c, 0x6d, 0x47, 0x49, 0x8a, 0x26, 0x2b, 0xee, 0x90, 0xda, 0x68, 0xb9, 0xbb, 0x9e, 0xd9,
	0xd5, 0x05, 0x68, 0x81, 0xa0, 0x45, 0xd0, 0x36, 0x40, 0x9b, 0x3e, 0x14, 0xe9, 0x53, 0x51, 0x14,
	0x45, 0x1e, 0xda, 0x87, 0xf6, 0xad, 0xfd, 0x17, 0xf2, 0x52, 0x20, 0x40, 0x81, 0x22, 0x40, 0x01,
	0xa2, 0x61, 0xff, 0x81, 0x02, 0x6d, 0x51, 0x54, 0x0f, 0x45, 0x31, 0x97, 0x9d, 0x9d, 0x5d, 0x92,
	0xb6, 0x61, 0x51, 0xc9, 0x1b, 0x79, 0xce, 0x99, 0xf3, 0x3b, 0x73, 0xe6, 0xcc, 0x99, 0x33, 0x67,
	0x07, 0xbc, 0xb2, 0x77, 0x9d, 0x96, 0x1c, 0x7f, 0xc1, 0x0a, 0x1c, 0x7c, 0x18, 0x62, 0x8f, 0x3a,
	0xbe, 0x47, 0x9f, 0xb6, 0x02, 0x87, 0x62, 0xb2, 0x8f, 0xc9, 0x42, 0xb0, 0x57, 0x67, 0x3c, 0x9a,
	0x16, 0x58, 0xd8, 0x7f, 0x76, 0xa1, 0x8e, 0x3d, 0x4c, 0xac, 0x10, 0xdb, 0xa5, 0x80, 0xf8, 0xa1,
	0x0f, 0xaf, 0x0b, 0x4d, 0xa5, 0x94, 0xe0, 0x5b, 0x4a, 0x53, 0x29, 0xd8, 0xab, 0x33, 0x1e, 0x4d,
	0x0b, 0x94, 0xf6, 0x9f, 0x9d, 0x79, 0xba, 0xee, 0x84, 0xbb, 0xd1, 0x4e, 0xa9, 0xea, 0x37, 0x16,
	0xea, 0x7e, 0xdd, 0x5f, 0xe0, 0x0a, 0x77, 0xa2, 0x1a, 0xff, 0xc7, 0xff, 0xf0, 0x5f, 0x02, 0x68,
	0xe6, 0xb9, 0xc4, 0xe4, 0x86, 0x55, 0xdd, 0x75, 0x3c, 0x4c, 0x8e, 0x12, 0x3b, 0x1b, 0x38, 0xb4,
	0x3a, 0x98, 0x37, 0xb3, 0xd0, 0x6d, 0x14, 0x89, 0xbc, 0xd0, 0x69, 0xe0, 0xb6, 0x01, 0x5f, 0x7a,
	0xd0, 0x00, 0x5a, 0xdd, 0xc5, 0x0d, 0x2b, 0x3b, 0xae, 0x78, 0x6c, 0x80, 0xc9, 0x65, 0xdf, 0xdb,
	0xc7, 0x84, 0x4d, 0x10, 0xe1, 0x7b, 0x11, 0xa6, 0x21, 0x2c, 0x83, 0xbe, 0xc8, 0xb1, 0x4d, 0x63,
	0xce, 0x98, 0x1f, 0x2e, 0x3f, 0xf3, 0x71, 0xb3, 0x70, 0xa6, 0xd5, 0x2c, 0xf4, 0xdd, 0x59, 0x5b,
	0x39, 0x6e, 0x16, 0x2e, 0x77, 0x43, 0x0a, 0x8f, 0x02, 0x4c, 0x4b, 0x77, 0xd6, 0x56, 0x10, 0x1b,
	0x0c, 0x5f, 0x06, 0x93, 0x36, 0xa6, 0x0e, 0xc1, 0xf6, 0xd2, 0xd6, 0xda, 0x5d, 0xa1, 0xdf, 0xcc,
	0x71, 0x8d, 0x17, 0xa4, 0xc6, 0xc9, 0x95, 0xac, 0x00, 0x6a, 0x1f, 0x03, 0xb7, 0xc1, 0x90, 0xbf,
	0xf3, 0x0e, 0xae, 0x86, 0xd4, 0xec, 0x9b, 0xeb, 0x9b, 0x1f, 0x59, 0x7c, 0xba, 0x94, 0x2c, 0x9e,
	0x32, 0x81, 0xaf, 0x98, 0x9c, 0x6c, 0x09, 0x59, 0x07, 0xab, 0xf1, 0xa2, 0x95, 0xc7, 0x25, 0xda,
	0xd0, 0xa6, 0xd0, 0x82, 0x62, 0x75, 0xc5, 0x5f, 0xe6, 0x00, 0xd4, 0x27, 0x4f, 0x03, 0xdf, 0xa3,
	0xb8, 0x27, 0xb3, 0xa7, 0x60, 0xa2, 0xca, 0x35, 0x87, 0xd8, 0x96, 0xb8, 0x66, 0xee, 0x51, 0xac,
	0x37, 0x25, 0xfe, 0xc4, 0x72, 0x46, 0x1d, 0x6a, 0x03, 0x80, 0xb7, 0xc1, 0x20, 0xc1, 0x34, 0x72,
	0x43, 0xb3, 0x6f, 0xce, 0x98, 0x1f, 0x59, 0xbc, 0xd6, 0x15, 0x8a, 0x87, 0x36, 0x0b, 0xbe, 0xd2,
	0xfe, 0xb3, 0xa5, 0x4a, 0x68, 0x85, 0x11, 0x2d, 0x9f, 0x95, 0x48, 0x83, 0x88, 0xeb, 0x40, 0x52,
	0x57, 0xf1, 0x3f, 0x06, 0x98, 0xd0, 0xbd, 0xb4, 0xef, 0xe0, 0x03, 0x48, 0xc0, 0x10, 0x11, 0xc1,
	0xc2, 0xfd, 0x34, 0xb2, 0x78, 0xb3, 0xf4, 0xa8, 0x3b, 0xaa, 0xd4, 0x16, 0x7f, 0xe5, 0x11, 0xb6,
	0x5c, 0xf2, 0x0f, 0x8a, 0x81, 0xe0, 0x3e, 0xc8, 0x13, 0xb9, 0x46, 0x3c, 0x90, 0x46, 0x16, 0xd7,
	0x7b, 0x03, 0x2a, 0x74, 0x96, 0x47, 0x5b, 0xcd, 0x42, 0x3e, 0xfe, 0x87, 0x14, 0x56, 0xf1, 0xe7,
	0x39, 0x30, 0xbb, 0x1c, 0xd1, 0xd0, 0x6f, 0x20, 0x4c, 0xfd, 0x88, 0x54, 0xf1, 0xb2, 0xef, 0x46,
	0x0d, 0x6f, 0x05, 0xd7, 0x1c, 0xcf, 0x09, 0x59, 0x8c, 0xce, 0x81, 0x7e, 0xcf, 0x6a, 0x60, 0x19,
	0x33, 0xa3, 0xd2, 0x93, 0xfd, 0xb7, 0xac, 0x06, 0x46, 0x9c, 0xc3, 0x24, 0x58, 0x88, 0xc8, 0x1d,
	0xa0, 0x24, 0x6e, 0x1f, 0x05, 0x18, 0x71, 0x0e, 0xbc, 0x0a, 0x06, 0x6b, 0x3e, 0x69, 0x58, 0x62,
	0xf5, 0x86, 0x93, 0xf5, 0xb8, 0xc1, 0xa9, 0x48, 0x72, 0xe1, 0xf3, 0x60, 0xc4, 0xc6, 0xb4, 0x4a,
	0x9c, 0x80, 0x41, 0x9b, 0xfd, 0x5c, 0xf8, 0x9c, 0x14, 0x1e, 0x59, 0x49, 0x58, 0x48, 0x97, 0x83,
	0xd7, 0x40, 0x3e, 0x20, 0x8e, 0x4f, 0x9c, 0xf0, 0xc8, 0x1c, 0x98, 0x33, 0xe6, 0x07, 0xca, 0x13,
	0x72, 0x4c, 0x7e, 0x4b, 0xd2, 0x91, 0x92, 0x60, 0xd2, 0xef, 0x50, 0xdf, 0xdb, 0xb2, 0xc2, 0x5d,
	0x73, 0x90, 0x23, 0x28, 0xe9, 0x57, 0x2b, 0x9b, 0xb7, 0x18, 0x1d, 0x29, 0x89, 0xe2, 0x9f, 0x0c,
	0x60, 0x66, 0x3d, 0x14, 0xbb, 0x17, 0xde, 0x00, 0x79, 0x1a, 0xb2, 0x9c, 0x53, 0x3f, 0x92, 0xfe,
	0x79, 0x32, 0x56, 0x55, 0x91, 0xf4, 0xe3, 0x66, 0x61, 0x3a, 0x19, 0x11, 0x53, 0xb9, 0x6f, 0xd4,
	0x58, 0x16, 0x72, 0x07, 0x78, 0x67, 0xd7, 0xf7, 0xf7, 0xe4, 0xea, 0x9f, 0x20, 0xe4, 0x5e, 0x13,
	0x8a, 0x12, 0x4c, 0x11, 0x72, 0x92, 0x8c, 0x62, 0xa0, 0xe2, 0xbf, 0x73, 0xd9, 0x89, 0x69, 0x8b,
	0xfe, 0x36, 0xc8, 0xb3, 0x2d, 0x64, 0x5b, 0xa1, 0x25, 0x37, 0xc1, 0x33, 0x0f, 0xb7, 0xe1, 0xc4,
	0x7e, 0xdd, 0xc0, 0xa1, 0x55, 0x86, 0xd2, 0x15, 0x20, 0xa1, 0x21, 0xa5, 0x15, 0x1e, 0x82, 0x7e,
	0x1a, 0xe0, 0xaa, 0x9c, 0xef, 0xdd, 0x13, 0x44, 0x7b, 0x97, 0x39, 0x54, 0x02, 0x5c, 0x4d, 0x82,
	0x91, 0xfd, 0x43, 0x1c, 0x11, 0xbe, 0x6b, 0x80, 0x41, 0xca, 0xf3, 0x82, 0xcc, 0x25, 0xdb, 0xa7,
	0x00, 0x9e, 0xc9, 0x3b, 0xe2, 0x3f, 0x92, 0xb8, 0xc5, 0x7f, 0xe4, 0xc0, 0xe5, 0x6e, 0x43, 0x97,
	0x7d, 0xcf, 0x16, 0x8b, 0xb0, 0x26, 0xf7, 0x95, 0x88, 0xac, 0xe7, 0xf5, 0x7d, 0x75, 0xdc, 0x2c,
	0x3c, 0xfe, 0x40, 0x05, 0xda, 0x06, 0xfc, 0xb2, 0x9a, 0xb2, 0xd8, 0xa4, 0x97, 0xd3, 0x86, 0x1d,
	0x37, 0x0b, 0xe3, 0x6a, 0x58, 0xda, 0x56, 0xb8, 0x0f, 0xa0, 0x6b, 0xd1, 0xf0, 0x36, 0xb1, 0x3c,
	0x2a, 0xd4, 0x3a, 0x0d, 0x2c, 0x3d, 0xf7, 0xe4, 0xc3, 0x05, 0x05, 0x1b, 0x51, 0x9e, 0x91, 0x90,
	0x70, 0xbd, 0x4d, 0x1b, 0xea, 0x80, 0xc0, 0x72, 0x06, 0xc1, 0x16, 0x55, 0x69, 0x40, 0xcb, 0xe1,
	0x8c, 0x8a, 0x24, 0x17, 0x3e, 0x01, 0x86, 0x1a, 0x98, 0x52, 0xab, 0x8e, 0xf9, 0xde, 0x1f, 0x4e,
	0x0e, 0xc5, 0x0d, 0x41, 0x46, 0x31, 0xbf, 0xf8, 0x4f, 0x03, 0x5c, 0xec, 0xe6, 0xb5, 0x75, 0x87,
	0x86, 0xf0, 0x1b, 0x6d, 0x61, 0x5f, 0x7a, 0xb8, 0x19, 0xb2, 0xd1, 0x3c, 0xe8, 0x55, 0x2a, 0x89,
	0x29, 0x5a, 0xc8, 0x1f, 0x80, 0x01, 0x27, 0xc4, 0x8d, 0xf8, 0xb4, 0x44, 0xbd, 0x0f, 0xbb, 0xf2,
	0x98, 0x84, 0x1f, 0x58, 0x63, 0x40, 0x48, 0xe0, 0x15, 0x3f, 0xca, 0x81, 0x4b, 0xdd, 0x86, 0xb0,
	0x3c, 0x4e, 0x99, 0xb3, 0x03, 0x37, 0x22, 0x96, 0x2b, 0x83, 0x4d, 0x39, 0x7b, 0x8b, 0x53, 0x91,
	0xe4, 0xb2, 0xdc, 0x49, 0x1d, 0xaf, 0x1e, 0xb9, 0x16, 0x91, 0x91, 0xa4, 0x26, 0x5c, 0x91, 0x74,
	0xa4, 0x24, 0x60, 0x09, 0x00, 0xba, 0xeb, 0x93, 0x90, 0x63, 0xf0, 0x0a, 0x67, 0xb8, 0x7c, 0x96,
	0x65, 0x84, 0x8a, 0xa2, 0x22, 0x4d, 0x82, 0x1d, 0x24, 0x7b, 0x8e, 0x67, 0xcb, 0x05, 0x57, 0x7b,
	0xf7, 0xa6, 0xe3, 0xd9, 0x88, 0x73, 0x18, 0xbe, 0xeb, 0xd0, 0x90, 0x51, 0xe4, 0x6a, 0xa7, 0x1c,
	0xce, 0x25, 0x95, 0x04, 0xc3, 0xaf, 0xb2, 0x04, 0xeb, 0x13, 0x07, 0x53, 0x73, 0x30, 0xc1, 0x5f,
	0x56, 0x54, 0xa4, 0x49, 0x14, 0xff, 0xdc, 0xdf, 0x3d, 0x3e, 0x58, 0x02, 0x81, 0x57, 0xc0, 0x40,
	0x9d, 0xf8, 0x51, 0x20, 0xbd, 0xa4, 0xbc, 0xfd, 0x32, 0x23, 0x22, 0xc1, 0x83, 0xdf, 0x02, 0x03,
	0x9e, 0x9c, 0x30, 0x8b, 0xa0, 0xd7, 0x7a, 0xbf, 0xcc, 0xdc, 0x5b, 0x09, 0xba, 0x70, 0xa4, 0x00,
	0x85, 0xcf, 0x81, 0x01, 0x5a, 0xf5, 0x03, 0x2c, 0x9d, 0x38, 0x1b, 0x0b, 0x55, 0x18, 0xf1, 0xb8,
	0x59, 0x18, 0x8b, 0xd5, 0x71, 0x02, 0x12, 0xc2, 0xf0, 0x7b, 0x06, 0xc8, 0xcb, 0xe3, 0x82, 0x9a,
	0x43, 0x3c, 0x3c, 0x5f, 0xef, 0xbd, 0xdd, 0xb2, 0xec, 0x4d, 0xd6, 0x4c, 0x12, 0x28, 0x52, 0xe0,
	0xf0, 0x3b, 0x06, 0x00, 0x55, 0x75, 0x76, 0x99, 0xc3, 0xdc, 0x87, 0x3d, 0xdb, 0x2a, 0xda, 0xa9,
	0x28, 0x02, 0x21, 0x29, 0x95, 0x34, 0x54, 0x58, 0x01, 0x53, 0x01, 0xc1, 0x5c, 0xf7, 0x1d, 0x6f,
	0xcf, 0xf3, 0x0f, 0xbc, 0x1b, 0x0e, 0x76, 0x6d, 0x6a, 0x82, 0x39, 0x63, 0x3e, 0x5f, 0xbe, 0x24,
	0xed, 0x9f, 0xda, 0xea, 0x24, 0x84, 0x3a, 0x8f, 0x2d, 0xbe, 0xd7, 0x97, 0xad, 0xb5, 0xb2, 0xe7,
	0x05, 0xfc, 0x40, 0x4c, 0x5e, 0xe4, 0x61, 0x6a, 0x1a, 0x7c, 0x21, 0xde, 0xec, 0xfd, 0x42, 0xa8,
	0x5c, 0x9f, 0x1c, 0xd2, 0x8a, 0x44, 0x91, 0x66, 0x02, 0xfc, 0x89, 0x01, 0xc6, 0xac, 0x6a, 0x15,
	0x07, 0x21, 0xb6, 0xc5, 0x36, 0xce, 0x9d, 0x6e, 0x54, 0x4f, 0x49, 0x83, 0xc6, 0x96, 0x74, 0x54,
	0x94, 0x36, 0x02, 0xbe, 0x08, 0xce, 0xd2, 0xd0, 0x27, 0xd8, 0x8e, 0x23, 0x48, 0x66, 0x17, 0xd8,
	0x6a, 0x16, 0xce, 0x56, 0x52, 0x1c, 0x94, 0x91, 0x2c, 0xb6, 0x06, 0x41, 0xe1, 0x01, 0x11, 0xfa,
	0x10, 0x45, 0xef, 0x55, 0x30, 0xc8, 0x67, 0x6a, 0x73, 0x87, 0xe4, 0xb5, 0xa3, 0x9e, 0x53, 0x91,
	0xe4, 0xb2, 0xe3, 0x89, 0xe1, 0xb3, 0xe3, 0xa9, 0x8f, 0x0b, 0xaa, 0xe3, 0xa9, 0x22, 0xc8, 0x28,
	0xe6, 0xc3, 0x45, 0x00, 0x6c, 0x1c, 0x10, 0xcc, 0x32, 0x92, 0x6d, 0x0e, 0x71, 0x69, 0xb5, 0x3e,
	0x2b, 0x8a, 0x83, 0x34, 0x29, 0x78, 0x03, 0xc0, 0xf8, 0x9f, 0xe3, 0x7b, 0xaf, 0x59, 0xc4, 0x73,
	0xbc, 0xba, 0x99, 0xe7, 0x66, 0x4f, 0xb3, 0xd3, 0x76, 0xa5, 0x8d, 0x8b, 0x3a, 0x8c, 0x80, 0xfb,
	0x60, 0x50, 0x5c, 0xa3, 0x79, 0xde, 0xe8, 0xe1, 0x8e, 0xbb, 0x6b, 0xb9, 0x8e, 0xcd, 0xa1, 0xca,
	0x80, 0xbb, 0x87, 0xa3, 0x20, 0x89, 0x06, 0xdf, 0x37, 0xc0, 0x28, 0x8d, 0x76, 0x88, 0x94, 0xa6,
	0x3c, 0xab, 0x8f, 0x2c, 0xde, 0xee, 0x15, 0x7c, 0x45, 0xd3, 0x5d, 0x9e, 0x68, 0x35, 0x0b, 0xa3,
	0x3a, 0x05, 0xa5, 0xb0, 0xe1, 0xef, 0x0c, 0x60, 0x5a, 0xb6, 0x08, 0x7d, 0xcb, 0xdd, 0x22, 0x8e,
	0x17, 0x62, 0x22, 0x2e, 0x44, 0xe2, 0xf8, 0xe8, 0x61, 0xad, 0x98, 0xbd, 0x67, 0x95, 0xe7, 0xe4,
	0x4a, 0x9b, 0x4b, 0x5d, 0x2c, 0x40, 0x5d, 0x6d, 0x63, 0x79, 0x63, 0x82, 0x62, 0x17, 0x57, 0x43,
	0x6b, 0xc7, 0xc5, 0x32, 0x57, 0x0d, 0x73, 0x83, 0xd7, 0x1e, 0xdd, 0xe0, 0x4a, 0x5a, 0x63, 0x72,
	0x5f, 0xcf, 0x30, 0x28, 0x6a, 0x03, 0x2f, 0xfe, 0xcb, 0xc8, 0x26, 0x3b, 0xcd, 0xef, 0x95, 0xaa,
	0xe5, 0x62, 0xb8, 0x02, 0x26, 0x58, 0x3d, 0x8e, 0x70, 0xe0, 0x3a, 0x55, 0x8b, 0xf2, 0xfb, 0x98,
	0xd8, 0x6f, 0x09, 0x50, 0x86, 0x8f, 0xda, 0x46, 0xc0, 0x57, 0x01, 0x14, 0x85, 0x6a, 0x4a, 0x8f,
	0xa8, 0x4d, 0x54, 0xc9, 0x59, 0x69, 0x93, 0x40, 0x1d, 0x46, 0xc1, 0x65, 0x30, 0xe9, 0x5a, 0x3b,
	0xd8, 0x15, 0xf3, 0xf3, 0x09, 0x57, 0x25, 0x6e, 0xac, 0x53, 0xad, 0x66, 0x61, 0x72, 0x3d, 0xcb,
	0x44, 0xed, 0xf2, 0xc5, 0xcb, 0xd9, 0xec, 0xa2, 0x4f, 0x5c, 0x94, 0xff, 0x1f, 0xe6, 0xc0, 0x4c,
	0xf7, 0x30, 0x85, 0xdf, 0x56, 0xc5, 0xba, 0xa8, 0x41, 0x5f, 0x3f, 0x85, 0xcd, 0x20, 0x2f, 0x28,
	0xa0, 0xfd, 0x72, 0x02, 0x8f, 0x58, 0x05, 0x61, 0xb9, 0x71, 0x23, 0x62, 0xfb, 0x34, 0xd0, 0x99,
	0xfe, 0xf2, 0xb0, 0xa8, 0x4b, 0x2c, 0x97, 0x97, 0x21, 0x96, 0x8b, 0x8b, 0x1f, 0xb5, 0x5d, 0xb6,
	0x93, 0xf4, 0x01, 0xbf, 0x6f, 0x80, 0x71, 0x3f, 0xc0, 0xde, 0xd2, 0xd6, 0xda, 0xdd, 0xff, 0x17,
	0x69, 0x44, 0x3a, 0xe8, 0x04, 0x31, 0xce, 0x6e, 0xfc, 0x42, 0xd7, 0x16, 0xf1, 0x03, 0x5a, 0x3e,
	0xd7, 0x6a, 0x16, 0xc6, 0x37, 0xd3, 0x28, 0x28, 0x0b, 0x5b, 0x6c, 0x80, 0xa9, 0xd5, 0xc3, 0x10,
	0x13, 0xcf, 0x72, 0x57, 0xfc, 0x6a, 0xd4, 0xc0, 0x5e, 0x28, 0x6c, 0xcc, 0x34, 0x30, 0x8c, 0x87,
	0x6c, 0x60, 0x5c, 0x02, 0x7d, 0x11, 0x71, 0x65, 0xd4, 0x8e, 0xa8, 0xb6, 0x1c, 0x5a, 0x47, 0x8c,
	0x5e, 0xbc, 0x0c, 0xfa, 0x99, 0x9d, 0xf0, 0x02, 0xe8, 0x23, 0xd6, 0x01, 0xd7, 0x3a, 0x5a, 0x1e,
	0x62, 0x22, 0xc8, 0x3a, 0x40, 0x8c, 0x56, 0xfc, 0xdb, 0x1c, 0x18, 0xcf, 0xcc, 0x05, 0xce, 0x80,
	0x9c, 0xea, 0xf5, 0x01, 0xa9, 0x34, 0xb7, 0xb6, 0x82, 0x72, 0x8e, 0x0d, 0x5f, 0x50, 0xf9, 0x5e,
	0x80, 0x16, 0xd4, 0xf1, 0xc5, 0xa9, 0xac, 0x50, 0x4c, 0xd4, 0x31, 0x43, 0xe2, 0x84, 0xcd, 0x6c,
	0xc0, 0x35, 0xb9, 0x2b, 0x84, 0x0d, 0xb8, 0x86, 0x18, 0xed, 0x51, 0xbb, 0x37, 0x71, 0xfb, 0x68,
	0xe0, 0x21, 0xda, 0x47, 0x83, 0xf7, 0x6d, 0x1f, 0x5d, 0x01, 0x03, 0xa1, 0x13, 0xba, 0x98, 0x9f,
	0x9d, 0x5a, 0x79, 0x7e, 0x9b, 0x11, 0x91, 0xe0, 0x41, 0x0c, 0x86, 0x6c, 0x5c, 0xb3, 0x22, 0x37,
	0xe4, 0xc7, 0xe4, 0xc8, 0xe2, 0xd7, 0x4f, 0x16, 0x3d, 0xa2, 0xbd, 0xb2, 0x22, 0x54, 0xa2, 0x58,
	0x37, 0x7c, 0x1c, 0x0c, 0x35, 0xac, 0x43, 0xa7, 0x11, 0x35, 0x78, 0x0d, 0x6b, 0x08, 0xb1, 0x0d,
	0x41, 0x42, 0x31, 0x8f, 0x25, 0x41, 0x7c, 0x58, 0x75, 0x23, 0xea, 0xec, 0x63, 0xc9, 0x94, 0x45,
	0xa6, 0x4a, 0x82, 0xab, 0x19, 0x3e, 0x6a, 0x1b, 0xc1, 0xc1, 0x1c, 0x8f, 0x0f, 0x1e, 0xd1, 0xc0,
	0x04, 0x09, 0xc5, 0xbc, 0x34, 0x98, 0x94, 0x1f, 0xed, 0x06, 0x26, 0x07, 0xb7, 0x8d, 0x80, 0x4f,
	0x81, 0xe1, 0x86, 0x75, 0xb8, 0x8e, 0xbd, 0x7a, 0xb8, 0x6b, 0x8e, 0xcd, 0x19, 0xf3, 0x7d, 0xe5,
	0xb1, 0x56, 0xb3, 0x30, 0xbc, 0x11, 0x13, 0x51, 0xc2, 0xe7, 0xc2, 0x8e, 0x27, 0x85, 0xcf, 0x6a,
	0xc2, 0x31, 0x11, 0x25, 0x7c, 0x56, 0x2b, 0x05, 0x56, 0xc8, 0xf6, 0x95, 0x39, 0x9e, 0xbe, 0xca,
	0x6f, 0x09, 0x32, 0x8a, 0xf9, 0x70, 0x1e, 0xe4, 0x1b, 0xd6, 0x21, 0xbf, 0xe5, 0x9a, 0x13, 0x5c,
	0x2d, 0x6f, 0x71, 0x6e, 0x48, 0x1a, 0x52, 0x5c, 0x2e, 0xe9, 0x78, 0x42, 0x72, 0x52, 0x93, 0x94,
	0x34, 0xa4, 0xb8, 0x2c, 0x7e, 0x23, 0xcf, 0xb9, 0x17, 0x61, 0x21, 0x0c, 0xb9, 0x67, 0x54, 0xfc,
	0xde, 0x49, 0x58, 0x48, 0x97, 0x63, 0xb7, 0xcc, 0x46, 0xe4, 0x86, 0x4e, 0xe0, 0xe2, 0xcd, 0x9a,
	0x79, 0x8e, 0xfb, 0x9f, 0x5f, 0x2e, 0x36, 0x14, 0x15, 0x69, 0x12, 0xf0, 0x6d, 0xd0, 0x8f, 0xbd,
	0xa8, 0x61, 0x9e, 0xe7, 0xe7, 0xf3, 0x49, 0xa3, 0x4f, 0xed, 0x97, 0x55, 0x2f, 0x6a, 0x20, 0xae,
	0x19, 0xbe, 0x00, 0xc6, 0x1a, 0xd6, 0x21, 0x4b, 0x02, 0x98, 0x84, 0xec, 0xea, 0x3b, 0xc5, 0xe7,
	0x3d, 0xc9, 0xca, 0xea, 0x0d, 0x9d, 0x81, 0xd2, 0x72, 0x7c, 0xa0, 0xe3, 0x69, 0x03, 0xa7, 0xb5,
	0x81, 0x3a, 0x03, 0xa5, 0xe5, 0x98, 0x93, 0x09, 0xbe, 0x17, 0x39, 0x04, 0xdb, 0xe6, 0xff, 0xf0,
	0x4a, 0x5c, 0x76, 0x9c, 0x05, 0x0d, 0x29, 0x2e, 0xbc, 0x17, 0x37, 0x41, 0x4c, 0xbe, 0xf9, 0xb6,
	0x7a, 0x96, 0xba, 0x37, 0xc9, 0x12, 0x21, 0xd6, 0x91, 0x38, 0x55, 0xf4, 0xf6, 0x07, 0xf4, 0xc0,
	0x80, 0xe5, 0xba, 0x9b, 0x35, 0xf3, 0xc2, 0x49, 0x2b, 0xa2, 0xec, 0x69, 0xa1, 0x32, 0xcc, 0x12,
	0xd3, 0x8f, 0x04, 0x0c, 0xc3, 0xf3, 0x3d, 0x16, 0x0b, 0x33, 0xa7, 0x86, 0xb7, 0xc9, 0xf4, 0x23,
	0x01, 0xc3, 0xe7, 0xe7, 0x1d, 0x6d, 0xd6, 0xcc, 0xc7, 0x4e, 0x6f, 0x7e, 0x4c, 0x3f, 0x12, 0x30,
	0xd0, 0x06, 0x7d, 0x9e, 0x1f, 0x9a, 0x17, 0x7b, 0x7d, 0xf6, 0xf2, 0xd3, 0xe4, 0x96, 0x1f, 0x22,
	0xa6, 0x1e, 0xfe, 0xd0, 0x00, 0x20, 0x48, 0x22, 0xf1, 0xd2, 0x49, 0x9b, 0x12, 0x19, 0xb4, 0x52,
	0x12, 0xbd, 0xab, 0x5e, 0x48, 0x8e, 0x92, 0x9b, 0x96, 0x16, 0xe5, 0x9a, 0x01, 0xf0, 0x67, 0x06,
	0x38, 0xaf, 0x17, 0xe0, 0xca, 0xb2, 0x59, 0xee, 0x87, 0xcd, 0x1e, 0x06, 0x72, 0xd9, 0xf7, 0xdd,
	0xb2, 0xd9, 0x6a, 0x16, 0xce, 0x2f, 0x75, 0x00, 0x44, 0x1d, 0xcd, 0x80, 0xbf, 0x32, 0xc0, 0xa4,
	0xcc, 0x8e, 0x9a, 0x71, 0x05, 0xee, 0xb6, 0xb7, 0x7b, 0xe8, 0xb6, 0x2c, 0x84, 0xf0, 0x9e, 0xfa,
	0xee, 0xd9, 0xc6, 0x47, 0xed, 0x56, 0xc1, 0xdf, 0x1a, 0x60, 0xd4, 0xc6, 0x01, 0xf6, 0x6c, 0xec,
	0x55, 0x99, 0x99, 0x73, 0x27, 0xed, 0x74, 0x64, 0xcd, 0x5c, 0xd1, 0xb4, 0x0b, 0x0b, 0x4b, 0xd2,
	0xc2, 0x51, 0x9d, 0x75, 0xdc, 0x2c, 0x4c, 0x27, 0x43, 0x75, 0x0e, 0x4a, 0x19, 0x08, 0x7f, 0x64,
	0x80, 0xf1, 0xc4, 0xed, 0xe2, 0x80, 0xb8, 0x7c, 0x3a, 0x0b, 0xcf, 0x4b, 0xd0, 0xa5, 0x34, 0x16,
	0xca, 0x82, 0xc3, 0x5f, 0x1b, 0xac, 0xda, 0x8a, 0x6f, 0x8f, 0xd4, 0x2c, 0x72, 0x0f, 0xbe, 0xd1,
	0x4b, 0x0f, 0x2a, 0xe5, 0xc2, 0x81, 0xd7, 0x92, 0x4a, 0x4e, 0x71, 0x8e, 0x9b, 0x85, 0x29, 0xdd,
	0x7f, 0x8a, 0x81, 0x74, 0xe3, 0xe0, 0x7b, 0x06, 0x18, 0xc5, 0x49, 0xc1, 0x4c, 0xcd, 0x2b, 0x27,
	0x75, 0x5d, 0xc7, 0xf2, 0x5b, 0x5c, 0xf0, 0x35, 0x16, 0x45, 0x29, 0x58, 0x56, 0xfb, 0xe1, 0x43,
	0xab, 0x11, 0xb8, 0xd8, 0xfc, 0xdf, 0xde, 0xd5, 0x7e, 0xab, 0x42, 0x25, 0x8a, 0x75, 0xc3, 0x6b,
	0x20, 0xef, 0x45, 0xae, 0xcb, 0xae, 0xc3, 0xe6, 0xe3, 0xbc, 0x8a, 0x50, 0x1d, 0xcf, 0x5b, 0x92,
	0x8e, 0x94, 0x04, 0xac, 0x81, 0xb9, 0xc3, 0x9b, 0xd1, 0x0e, 0x26, 0x1e, 0x0e, 0x31, 0xed, 0xd8,
	0x52, 0x34, 0xaf, 0x72, 0x2d, 0x33, 0xad, 0x66, 0x61, 0x7a, 0xbb, 0x73, 0xd3, 0xf1, 0x81, 0x3a,
	0xe0, 0x9b, 0xe0, 0x31, 0x4d, 0x66, 0xb5, 0xb1, 0x83, 0x6d, 0x1b, 0xdb, 0xf1, 0x45, 0xcb, 0xfc,
	0x3f, 0x0e, 0xa1, 0xf6, 0xf1, 0x76, 0x56, 0x00, 0xdd, 0x6f, 0x34, 0x5c, 0x07, 0xd3, 0x1a, 0x7b,
	0xcd, 0x0b, 0x37, 0x49, 0x25, 0x24, 0x8e, 0x57, 0x37, 0xe7, 0xb9, 0xde, 0xf3, 0xf1, 0xee, 0xdb,
	0xd6, 0x78, 0xa8, 0xcb, 0x18, 0xf8, 0x4a, 0x4a, 0x1b, 0xff, 0x94, 0x62, 0x05, 0x37, 0xf1, 0x11,
	0x35, 0x9f, 0xe0, 0xc5, 0x05, 0x5f, 0xe7, 0x6d, 0x8d, 0x8e, 0xba, 0xc8, 0xc3, 0x97, 0xc0, 0xb9,
	0x0c, 0x87, 0xdd, 0x2b, 0xcc, 0x27, 0xc5, 0x05, 0x81, 0x55, 0xa2, 0xdb, 0x31, 0x11, 0x75, 0x92,
	0x84, 0x5f, 0x05, 0x50, 0x23, 0x6f, 0x58, 0x01, 0x1f, 0xff, 0x94, 0xb8, 0xab, 0xb0, 0x15, 0xdd,
	0x96, 0x34, 0xd4, 0x41, 0x0e, 0x7e, 0x68, 0xa4, 0x66, 0x92, 0xdc, 0x66, 0xa9, 0x79, 0x8d, 0x6f,
	0xd8, 0x57, 0x1e, 0x3d, 0x00, 0x13, 0x65, 0x28, 0x72, 0xb1, 0xe6, 0x61, 0x0d, 0x05, 0x75, 0x41,
	0x9f, 0x61, 0x97, 0xe9, 0x4c, 0x0e, 0x87, 0x13, 0xa0, 0x6f, 0x0f, 0xcb, 0x0f, 0xd9, 0x88, 0xfd,
	0x84, 0x6f, 0x81, 0x81, 0x7d, 0xcb, 0x8d, 0xe2, 0x56, 0x40, 0xef, 0xce, 0x7a, 0x24, 0xf4, 0xbe,
	0x98, 0xbb, 0x6e, 0xcc, 0x7c, 0x60, 0x80, 0xe9, 0xce, 0xa7, 0xca, 0x17, 0x65, 0xd1, 0x4f, 0x0d,
	0x30, 0xd9, 0x76, 0x80, 0x74, 0x30, 0xc6, 0x4d, 0x1b, 0x73, 0xb7, 0x87, 0x27, 0x81, 0xd8, 0x08,
	0xbc, 0xa2, 0xd5, 0x2d, 0xfb, 0x81, 0x01, 0x26, 0xb2, 0x89, 0xf9, 0x0b, 0xf2, 0x52, 0xf1, 0xfd,
	0x1c, 0x98, 0xee, 0x5c, 0x83, 0xc3, 0x86, 0xea, 0x2e, 0xf4, 0xbc, 0x41, 0xd3, 0xa9, 0x89, 0xfc,
	0xae, 0x01, 0x46, 0xde, 0x51, 0x72, 0xf1, 0xf7, 0xd5, 0x5e, 0x76, 0x85, 0xe2, 0xa3, 0x2f, 0x61,
	0x50, 0xa4, 0x43, 0x16, 0x7f, 0x63, 0x80, 0xa9, 0x8e, 0xc7, 0x39, 0xbc, 0x0a, 0x06, 0x2d, 0xd7,
	0xf5, 0x0f, 0x44, 0x37, 0x4f, 0xfb, 0x50, 0xb0, 0xc4, 0xa9, 0x48, 0x72, 0x35, 0x9f, 0xe5, 0x3e,
	0x07, 0x9f, 0x15, 0x7f, 0x6f, 0x80, 0x8b, 0xf7, 0x8b, 0xba, 0xcf, 0x7b, 0x0d, 0xe7, 0x41, 0x5e,
	0x16, 0xdb, 0x47, 0x7c, 0xfd, 0x64, 0x76, 0x95, 0x19, 0x81, 0xbf, 0xdf, 0x11, 0xbf, 0x8a, 0x2f,
	0x81, 0xf1, 0x4c, 0x03, 0x3a, 0xf5, 0xa4, 0xc7, 0x78, 0xe0, 0x93, 0x9e, 0x5f, 0x18, 0x60, 0xa2,
	0x82, 0xc9, 0xbe, 0x53, 0xc5, 0x08, 0xd7, 0x30, 0xc1, 0x5e, 0x15, 0xc3, 0x05, 0x30, 0xcc, 0x3f,
	0xa0, 0x06, 0x56, 0x35, 0xfe, 0xec, 0x33, 0x29, 0x75, 0x0c, 0xdf, 0x8a, 0x19, 0x28, 0x91, 0x51,
	0x9f, 0x88, 0x72, 0x5d, 0x3f, 0x11, 0x5d, 0x04, 0xfd, 0x41, 0xd2, 0x41, 0xce, 0x33, 0x2e, 0xb7,
	0x84, 0x53, 0x39, 0xd7, 0x27, 0x21, 0x6f, 0x93, 0x0d, 0x48, 0xae, 0x4f, 0x42, 0xc4, 0xa9, 0xc5,
	0x3f, 0xe6, 0xc0, 0xd9, 0x74, 0x82, 0x67, 0x80, 0x24, 0x72, 0xdb, 0xbe, 0x49, 0x31, 0x1e, 0xe2,
	0x1c, 0xfd, 0x29, 0x44, 0xee, 0xfe, 0x4f, 0x21, 0xe0, 0xcb, 0x60, 0x52, 0xfe, 0x5c, 0x3d, 0x0c,
	0x08, 0xa6, 0xfc, 0x63, 0x6b, 0x5f, 0xfa, 0x09, 0xe3, 0x46, 0x56, 0x00, 0xb5, 0x8f, 0x81, 0x5f,
	0xc9, 0x3c, 0xd3, 0xb8, 0x92, 0x3c, 0xd1, 0x60, 0xc5, 0x21, 0x5f, 0x9f, 0xbb, 0x2c, 0x67, 0xac,
	0x12, 0xe2, 0x93, 0xcc, 0xdb, 0x8d, 0x05, 0x30, 0x5c, 0x63, 0x02, 0x7c, 0xe1, 0x06, 0xd2, 0x4e,
	0xbf, 0x11, 0x33, 0x50, 0x22, 0x03, 0xbf, 0x06, 0xc6, 0xfd, 0x40, 0x94, 0xc1, 0x9b, 0xae, 0x5d,
	0xc1, 0x6e, 0x8d, 0xb7, 0x04, 0xf3, 0x71, 0xdf, 0x36, 0xc5, 0x42, 0x59, 0xd9, 0xe2, 0x1f, 0x0c,
	0x70, 0x2e, 0x7e, 0x1f, 0xe5, 0x3a, 0xd8, 0x0b, 0x97, 0x7d, 0xaf, 0xe6, 0xd4, 0xe1, 0x05, 0xd1,
	0x7f, 0xd5, 0x9a, 0x9a, 0x71, 0xef, 0x15, 0xde, 0x03, 0x43, 0x54, 0xc4, 0x8a, 0xdc, 0x07, 0xaf,
	0x9e, 0xe4, 0x83, 0x4a, 0x3a, 0xe8, 0x44, 0xf9, 0x18, 0x53, 0x63, 0x1c, 0xb6, 0x15, 0xaa, 0x56,
	0x39, 0xf2, 0x6c, 0xd9, 0x83, 0x1f, 0x15, 0x5b, 0x61, 0x79, 0x49, 0xd0, 0x90, 0xe2, 0x16, 0xff,
	0x6e, 0x80, 0xc9, 0xb6, 0xf7, 0x5e, 0xf0, 0xbb, 0x06, 0x18, 0xad, 0x6a, 0xd3, 0x93, 0x09, 0x65,
	0xe3, 0xe4, 0x6f, 0xca, 0x34, 0xa5, 0xa2, 0x06, 0xd3, 0x29, 0x28, 0x05, 0x0a, 0xb7, 0x81, 0x59,
	0xcd, 0x3c, 0xad, 0xcc, 0x7c, 0xac, 0xbd, 0xd8, 0x6a, 0x16, 0xcc, 0xe5, 0x2e, 0x32, 0xa8, 0xeb,
	0xe8, 0xf2, 0x37, 0x3f, 0xfe, 0x6c, 0xf6, 0xcc, 0x27, 0x9f, 0xcd, 0x9e, 0xf9, 0xf4, 0xb3, 0xd9,
	0x33, 0xef, 0xb6, 0x66, 0x8d, 0x8f, 0x5b, 0xb3, 0xc6, 0x27, 0xad, 0x59, 0xe3, 0xd3, 0xd6, 0xac,
	0xf1, 0x97, 0xd6, 0xac, 0xf1, 0xe3, 0xbf, 0xce, 0x9e, 0x79, 0xe3, 0xfa, 0xa3, 0x3e, 0xa8, 0xfe,
	0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xa3, 0x1c, 0x7a, 0x10, 0x8b, 0x2d, 0x00, 0x00,
}

func (m *ConversionRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConversionRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ConversionRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Objects) > 0 {
		for iNdEx := len(m.Objects) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Objects[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	i -= len(m.DesiredAPIVersion)
	copy(dAtA[i:], m.DesiredAPIVersion)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.DesiredAPIVersion)))
	i--
	dAtA[i] = 0x12
	i -= len(m.UID)
	copy(dAtA[i:], m.UID)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.UID)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *ConversionResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConversionResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ConversionResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	{
		size, err := m.Result.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x1a
	if len(m.ConvertedObjects) > 0 {
		for iNdEx := len(m.ConvertedObjects) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.ConvertedObjects[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	i -= len(m.UID)
	copy(dAtA[i:], m.UID)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.UID)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *ConversionReview) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConversionReview) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ConversionReview) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Response != nil {
		{
			size, err := m.Response.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Request != nil {
		{
			size, err := m.Request.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CustomResourceColumnDefinition) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CustomResourceColumnDefinition) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CustomResourceColumnDefinition) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.JSONPath)
	copy(dAtA[i:], m.JSONPath)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.JSONPath)))
	i--
	dAtA[i] = 0x32
	i = encodeVarintGenerated(dAtA, i, uint64(m.Priority))
	i--
	dAtA[i] = 0x28
	i -= len(m.Description)
	copy(dAtA[i:], m.Description)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Description)))
	i--
	dAtA[i] = 0x22
	i -= len(m.Format)
	copy(dAtA[i:], m.Format)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Format)))
	i--
	dAtA[i] = 0x1a
	i -= len(m.Type)
	copy(dAtA[i:], m.Type)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Type)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Name)
	copy(dAtA[i:], m.Name)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Name)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *CustomResourceConversion) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CustomResourceConversion) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CustomResourceConversion) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Webhook != nil {
		{
			size, err := m.Webhook.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	i -= len(m.Strategy)
	copy(dAtA[i:], m.Strategy)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Strategy)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *CustomResourceDefinition) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CustomResourceDefinition) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CustomResourceDefinition) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	{
		size, err := m.Status.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x1a
	{
		size, err := m.Spec.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	{
		size, err := m.ObjectMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *CustomResourceDefinitionCondition) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CustomResourceDefinitionCondition) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CustomResourceDefinitionCondition) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.Message)
	copy(dAtA[i:], m.Message)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Message)))
	i--
	dAtA[i] = 0x2a
	i -= len(m.Reason)
	copy(dAtA[i:], m.Reason)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Reason)))
	i--
	dAtA[i] = 0x22
	{
		size, err := m.LastTransitionTime.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x1a
	i -= len(m.Status)
	copy(dAtA[i:], m.Status)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Status)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Type)
	copy(dAtA[i:], m.Type)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Type)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *CustomResourceDefinitionList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CustomResourceDefinitionList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CustomResourceDefinitionList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Items) > 0 {
		for iNdEx := len(m.Items) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Items[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	{
		size, err := m.ListMeta.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *CustomResourceDefinitionNames) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CustomResourceDefinitionNames) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CustomResourceDefinitionNames) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Categories) > 0 {
		for iNdEx := len(m.Categories) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Categories[iNdEx])
			copy(dAtA[i:], m.Categories[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.Categories[iNdEx])))
			i--
			dAtA[i] = 0x32
		}
	}
	i -= len(m.ListKind)
	copy(dAtA[i:], m.ListKind)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.ListKind)))
	i--
	dAtA[i] = 0x2a
	i -= len(m.Kind)
	copy(dAtA[i:], m.Kind)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Kind)))
	i--
	dAtA[i] = 0x22
	if len(m.ShortNames) > 0 {
		for iNdEx := len(m.ShortNames) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ShortNames[iNdEx])
			copy(dAtA[i:], m.ShortNames[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.ShortNames[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	i -= len(m.Singular)
	copy(dAtA[i:], m.Singular)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Singular)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Plural)
	copy(dAtA[i:], m.Plural)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Plural)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *CustomResourceDefinitionSpec) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CustomResourceDefinitionSpec) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CustomResourceDefinitionSpec) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i--
	if m.PreserveUnknownFields {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x50
	if m.Conversion != nil {
		{
			size, err := m.Conversion.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	if len(m.Versions) > 0 {
		for iNdEx := len(m.Versions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Versions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x3a
		}
	}
	i -= len(m.Scope)
	copy(dAtA[i:], m.Scope)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Scope)))
	i--
	dAtA[i] = 0x22
	{
		size, err := m.Names.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x1a
	i -= len(m.Group)
	copy(dAtA[i:], m.Group)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Group)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *CustomResourceDefinitionStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CustomResourceDefinitionStatus) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CustomResourceDefinitionStatus) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.StoredVersions) > 0 {
		for iNdEx := len(m.StoredVersions) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.StoredVersions[iNdEx])
			copy(dAtA[i:], m.StoredVersions[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.StoredVersions[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	{
		size, err := m.AcceptedNames.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintGenerated(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	if len(m.Conditions) > 0 {
		for iNdEx := len(m.Conditions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Conditions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *CustomResourceDefinitionVersion) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CustomResourceDefinitionVersion) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CustomResourceDefinitionVersion) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SelectableFields) > 0 {
		for iNdEx := len(m.SelectableFields) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.SelectableFields[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x4a
		}
	}
	if m.DeprecationWarning != nil {
		i -= len(*m.DeprecationWarning)
		copy(dAtA[i:], *m.DeprecationWarning)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.DeprecationWarning)))
		i--
		dAtA[i] = 0x42
	}
	i--
	if m.Deprecated {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x38
	if len(m.AdditionalPrinterColumns) > 0 {
		for iNdEx := len(m.AdditionalPrinterColumns) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.AdditionalPrinterColumns[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x32
		}
	}
	if m.Subresources != nil {
		{
			size, err := m.Subresources.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.Schema != nil {
		{
			size, err := m.Schema.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	i--
	if m.Storage {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x18
	i--
	if m.Served {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x10
	i -= len(m.Name)
	copy(dAtA[i:], m.Name)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Name)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *CustomResourceSubresourceScale) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CustomResourceSubresourceScale) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CustomResourceSubresourceScale) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.LabelSelectorPath != nil {
		i -= len(*m.LabelSelectorPath)
		copy(dAtA[i:], *m.LabelSelectorPath)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.LabelSelectorPath)))
		i--
		dAtA[i] = 0x1a
	}
	i -= len(m.StatusReplicasPath)
	copy(dAtA[i:], m.StatusReplicasPath)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.StatusReplicasPath)))
	i--
	dAtA[i] = 0x12
	i -= len(m.SpecReplicasPath)
	copy(dAtA[i:], m.SpecReplicasPath)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.SpecReplicasPath)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *CustomResourceSubresourceStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CustomResourceSubresourceStatus) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CustomResourceSubresourceStatus) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *CustomResourceSubresources) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CustomResourceSubresources) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CustomResourceSubresources) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Scale != nil {
		{
			size, err := m.Scale.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Status != nil {
		{
			size, err := m.Status.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CustomResourceValidation) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CustomResourceValidation) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CustomResourceValidation) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.OpenAPIV3Schema != nil {
		{
			size, err := m.OpenAPIV3Schema.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ExternalDocumentation) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExternalDocumentation) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExternalDocumentation) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.URL)
	copy(dAtA[i:], m.URL)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.URL)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Description)
	copy(dAtA[i:], m.Description)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Description)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *JSON) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *JSON) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *JSON) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Raw != nil {
		i -= len(m.Raw)
		copy(dAtA[i:], m.Raw)
		i = encodeVarintGenerated(dAtA, i, uint64(len(m.Raw)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *JSONSchemaProps) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *JSONSchemaProps) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *JSONSchemaProps) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.XValidations) > 0 {
		for iNdEx := len(m.XValidations) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.XValidations[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2
			i--
			dAtA[i] = 0xe2
		}
	}
	if m.XMapType != nil {
		i -= len(*m.XMapType)
		copy(dAtA[i:], *m.XMapType)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.XMapType)))
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xda
	}
	if m.XListType != nil {
		i -= len(*m.XListType)
		copy(dAtA[i:], *m.XListType)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.XListType)))
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xd2
	}
	if len(m.XListMapKeys) > 0 {
		for iNdEx := len(m.XListMapKeys) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.XListMapKeys[iNdEx])
			copy(dAtA[i:], m.XListMapKeys[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.XListMapKeys[iNdEx])))
			i--
			dAtA[i] = 0x2
			i--
			dAtA[i] = 0xca
		}
	}
	i--
	if m.XIntOrString {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0xc0
	i--
	if m.XEmbeddedResource {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0xb8
	if m.XPreserveUnknownFields != nil {
		i--
		if *m.XPreserveUnknownFields {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xb0
	}
	i--
	if m.Nullable {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0xa8
	if m.Example != nil {
		{
			size, err := m.Example.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xa2
	}
	if m.ExternalDocs != nil {
		{
			size, err := m.ExternalDocs.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0x9a
	}
	if len(m.Definitions) > 0 {
		keysForDefinitions := make([]string, 0, len(m.Definitions))
		for k := range m.Definitions {
			keysForDefinitions = append(keysForDefinitions, string(k))
		}
		github_com_gogo_protobuf_sortkeys.Strings(keysForDefinitions)
		for iNdEx := len(keysForDefinitions) - 1; iNdEx >= 0; iNdEx-- {
			v := m.Definitions[string(keysForDefinitions[iNdEx])]
			baseI := i
			{
				size, err := (&v).MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
			i -= len(keysForDefinitions[iNdEx])
			copy(dAtA[i:], keysForDefinitions[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(keysForDefinitions[iNdEx])))
			i--
			dAtA[i] = 0xa
			i = encodeVarintGenerated(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x2
			i--
			dAtA[i] = 0x92
		}
	}
	if m.AdditionalItems != nil {
		{
			size, err := m.AdditionalItems.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0x8a
	}
	if len(m.Dependencies) > 0 {
		keysForDependencies := make([]string, 0, len(m.Dependencies))
		for k := range m.Dependencies {
			keysForDependencies = append(keysForDependencies, string(k))
		}
		github_com_gogo_protobuf_sortkeys.Strings(keysForDependencies)
		for iNdEx := len(keysForDependencies) - 1; iNdEx >= 0; iNdEx-- {
			v := m.Dependencies[string(keysForDependencies[iNdEx])]
			baseI := i
			{
				size, err := (&v).MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
			i -= len(keysForDependencies[iNdEx])
			copy(dAtA[i:], keysForDependencies[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(keysForDependencies[iNdEx])))
			i--
			dAtA[i] = 0xa
			i = encodeVarintGenerated(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x2
			i--
			dAtA[i] = 0x82
		}
	}
	if len(m.PatternProperties) > 0 {
		keysForPatternProperties := make([]string, 0, len(m.PatternProperties))
		for k := range m.PatternProperties {
			keysForPatternProperties = append(keysForPatternProperties, string(k))
		}
		github_com_gogo_protobuf_sortkeys.Strings(keysForPatternProperties)
		for iNdEx := len(keysForPatternProperties) - 1; iNdEx >= 0; iNdEx-- {
			v := m.PatternProperties[string(keysForPatternProperties[iNdEx])]
			baseI := i
			{
				size, err := (&v).MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
			i -= len(keysForPatternProperties[iNdEx])
			copy(dAtA[i:], keysForPatternProperties[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(keysForPatternProperties[iNdEx])))
			i--
			dAtA[i] = 0xa
			i = encodeVarintGenerated(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xfa
		}
	}
	if m.AdditionalProperties != nil {
		{
			size, err := m.AdditionalProperties.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xf2
	}
	if len(m.Properties) > 0 {
		keysForProperties := make([]string, 0, len(m.Properties))
		for k := range m.Properties {
			keysForProperties = append(keysForProperties, string(k))
		}
		github_com_gogo_protobuf_sortkeys.Strings(keysForProperties)
		for iNdEx := len(keysForProperties) - 1; iNdEx >= 0; iNdEx-- {
			v := m.Properties[string(keysForProperties[iNdEx])]
			baseI := i
			{
				size, err := (&v).MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
			i -= len(keysForProperties[iNdEx])
			copy(dAtA[i:], keysForProperties[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(keysForProperties[iNdEx])))
			i--
			dAtA[i] = 0xa
			i = encodeVarintGenerated(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xea
		}
	}
	if m.Not != nil {
		{
			size, err := m.Not.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xe2
	}
	if len(m.AnyOf) > 0 {
		for iNdEx := len(m.AnyOf) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.AnyOf[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xda
		}
	}
	if len(m.OneOf) > 0 {
		for iNdEx := len(m.OneOf) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.OneOf[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xd2
		}
	}
	if len(m.AllOf) > 0 {
		for iNdEx := len(m.AllOf) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.AllOf[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xca
		}
	}
	if m.Items != nil {
		{
			size, err := m.Items.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xc2
	}
	if len(m.Required) > 0 {
		for iNdEx := len(m.Required) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Required[iNdEx])
			copy(dAtA[i:], m.Required[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.Required[iNdEx])))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xba
		}
	}
	if m.MinProperties != nil {
		i = encodeVarintGenerated(dAtA, i, uint64(*m.MinProperties))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xb0
	}
	if m.MaxProperties != nil {
		i = encodeVarintGenerated(dAtA, i, uint64(*m.MaxProperties))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xa8
	}
	if len(m.Enum) > 0 {
		for iNdEx := len(m.Enum) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Enum[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xa2
		}
	}
	if m.MultipleOf != nil {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(*m.MultipleOf))))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x99
	}
	i--
	if m.UniqueItems {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0x90
	if m.MinItems != nil {
		i = encodeVarintGenerated(dAtA, i, uint64(*m.MinItems))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x88
	}
	if m.MaxItems != nil {
		i = encodeVarintGenerated(dAtA, i, uint64(*m.MaxItems))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x80
	}
	i -= len(m.Pattern)
	copy(dAtA[i:], m.Pattern)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Pattern)))
	i--
	dAtA[i] = 0x7a
	if m.MinLength != nil {
		i = encodeVarintGenerated(dAtA, i, uint64(*m.MinLength))
		i--
		dAtA[i] = 0x70
	}
	if m.MaxLength != nil {
		i = encodeVarintGenerated(dAtA, i, uint64(*m.MaxLength))
		i--
		dAtA[i] = 0x68
	}
	i--
	if m.ExclusiveMinimum {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x60
	if m.Minimum != nil {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(*m.Minimum))))
		i--
		dAtA[i] = 0x59
	}
	i--
	if m.ExclusiveMaximum {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x50
	if m.Maximum != nil {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(*m.Maximum))))
		i--
		dAtA[i] = 0x49
	}
	if m.Default != nil {
		{
			size, err := m.Default.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	i -= len(m.Title)
	copy(dAtA[i:], m.Title)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Title)))
	i--
	dAtA[i] = 0x3a
	i -= len(m.Format)
	copy(dAtA[i:], m.Format)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Format)))
	i--
	dAtA[i] = 0x32
	i -= len(m.Type)
	copy(dAtA[i:], m.Type)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Type)))
	i--
	dAtA[i] = 0x2a
	i -= len(m.Description)
	copy(dAtA[i:], m.Description)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Description)))
	i--
	dAtA[i] = 0x22
	if m.Ref != nil {
		i -= len(*m.Ref)
		copy(dAtA[i:], *m.Ref)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.Ref)))
		i--
		dAtA[i] = 0x1a
	}
	i -= len(m.Schema)
	copy(dAtA[i:], m.Schema)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Schema)))
	i--
	dAtA[i] = 0x12
	i -= len(m.ID)
	copy(dAtA[i:], m.ID)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.ID)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *JSONSchemaPropsOrArray) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *JSONSchemaPropsOrArray) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *JSONSchemaPropsOrArray) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.JSONSchemas) > 0 {
		for iNdEx := len(m.JSONSchemas) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.JSONSchemas[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGenerated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Schema != nil {
		{
			size, err := m.Schema.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *JSONSchemaPropsOrBool) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *JSONSchemaPropsOrBool) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *JSONSchemaPropsOrBool) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Schema != nil {
		{
			size, err := m.Schema.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	i--
	if m.Allows {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x8
	return len(dAtA) - i, nil
}

func (m *JSONSchemaPropsOrStringArray) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *JSONSchemaPropsOrStringArray) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *JSONSchemaPropsOrStringArray) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Property) > 0 {
		for iNdEx := len(m.Property) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Property[iNdEx])
			copy(dAtA[i:], m.Property[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.Property[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Schema != nil {
		{
			size, err := m.Schema.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SelectableField) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SelectableField) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SelectableField) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.JSONPath)
	copy(dAtA[i:], m.JSONPath)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.JSONPath)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *ServiceReference) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ServiceReference) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ServiceReference) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Port != nil {
		i = encodeVarintGenerated(dAtA, i, uint64(*m.Port))
		i--
		dAtA[i] = 0x20
	}
	if m.Path != nil {
		i -= len(*m.Path)
		copy(dAtA[i:], *m.Path)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.Path)))
		i--
		dAtA[i] = 0x1a
	}
	i -= len(m.Name)
	copy(dAtA[i:], m.Name)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Name)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Namespace)
	copy(dAtA[i:], m.Namespace)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Namespace)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *ValidationRule) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ValidationRule) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ValidationRule) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.OptionalOldSelf != nil {
		i--
		if *m.OptionalOldSelf {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x30
	}
	i -= len(m.FieldPath)
	copy(dAtA[i:], m.FieldPath)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.FieldPath)))
	i--
	dAtA[i] = 0x2a
	if m.Reason != nil {
		i -= len(*m.Reason)
		copy(dAtA[i:], *m.Reason)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.Reason)))
		i--
		dAtA[i] = 0x22
	}
	i -= len(m.MessageExpression)
	copy(dAtA[i:], m.MessageExpression)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.MessageExpression)))
	i--
	dAtA[i] = 0x1a
	i -= len(m.Message)
	copy(dAtA[i:], m.Message)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Message)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Rule)
	copy(dAtA[i:], m.Rule)
	i = encodeVarintGenerated(dAtA, i, uint64(len(m.Rule)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *WebhookClientConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WebhookClientConfig) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WebhookClientConfig) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.URL != nil {
		i -= len(*m.URL)
		copy(dAtA[i:], *m.URL)
		i = encodeVarintGenerated(dAtA, i, uint64(len(*m.URL)))
		i--
		dAtA[i] = 0x1a
	}
	if m.CABundle != nil {
		i -= len(m.CABundle)
		copy(dAtA[i:], m.CABundle)
		i = encodeVarintGenerated(dAtA, i, uint64(len(m.CABundle)))
		i--
		dAtA[i] = 0x12
	}
	if m.Service != nil {
		{
			size, err := m.Service.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *WebhookConversion) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WebhookConversion) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WebhookConversion) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ConversionReviewVersions) > 0 {
		for iNdEx := len(m.ConversionReviewVersions) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ConversionReviewVersions[iNdEx])
			copy(dAtA[i:], m.ConversionReviewVersions[iNdEx])
			i = encodeVarintGenerated(dAtA, i, uint64(len(m.ConversionReviewVersions[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.ClientConfig != nil {
		{
			size, err := m.ClientConfig.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGenerated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}

func encodeVarintGenerated(dAtA []byte, offset int, v uint64) int {
	offset -= sovGenerated(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *ConversionRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.UID)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.DesiredAPIVersion)
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Objects) > 0 {
		for _, e := range m.Objects {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *ConversionResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.UID)
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.ConvertedObjects) > 0 {
		for _, e := range m.ConvertedObjects {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	l = m.Result.Size()
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func (m *ConversionReview) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Request != nil {
		l = m.Request.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.Response != nil {
		l = m.Response.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *CustomResourceColumnDefinition) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Type)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Format)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Description)
	n += 1 + l + sovGenerated(uint64(l))
	n += 1 + sovGenerated(uint64(m.Priority))
	l = len(m.JSONPath)
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func (m *CustomResourceConversion) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Strategy)
	n += 1 + l + sovGenerated(uint64(l))
	if m.Webhook != nil {
		l = m.Webhook.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *CustomResourceDefinition) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ObjectMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	l = m.Spec.Size()
	n += 1 + l + sovGenerated(uint64(l))
	l = m.Status.Size()
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func (m *CustomResourceDefinitionCondition) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Type)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Status)
	n += 1 + l + sovGenerated(uint64(l))
	l = m.LastTransitionTime.Size()
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Reason)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Message)
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func (m *CustomResourceDefinitionList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.ListMeta.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Items) > 0 {
		for _, e := range m.Items {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *CustomResourceDefinitionNames) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Plural)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Singular)
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.ShortNames) > 0 {
		for _, s := range m.ShortNames {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	l = len(m.Kind)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.ListKind)
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Categories) > 0 {
		for _, s := range m.Categories {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *CustomResourceDefinitionSpec) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Group)
	n += 1 + l + sovGenerated(uint64(l))
	l = m.Names.Size()
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Scope)
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.Versions) > 0 {
		for _, e := range m.Versions {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	if m.Conversion != nil {
		l = m.Conversion.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	n += 2
	return n
}

func (m *CustomResourceDefinitionStatus) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Conditions) > 0 {
		for _, e := range m.Conditions {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	l = m.AcceptedNames.Size()
	n += 1 + l + sovGenerated(uint64(l))
	if len(m.StoredVersions) > 0 {
		for _, s := range m.StoredVersions {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *CustomResourceDefinitionVersion) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	n += 1 + l + sovGenerated(uint64(l))
	n += 2
	n += 2
	if m.Schema != nil {
		l = m.Schema.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.Subresources != nil {
		l = m.Subresources.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	if len(m.AdditionalPrinterColumns) > 0 {
		for _, e := range m.AdditionalPrinterColumns {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	n += 2
	if m.DeprecationWarning != nil {
		l = len(*m.DeprecationWarning)
		n += 1 + l + sovGenerated(uint64(l))
	}
	if len(m.SelectableFields) > 0 {
		for _, e := range m.SelectableFields {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *CustomResourceSubresourceScale) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.SpecReplicasPath)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.StatusReplicasPath)
	n += 1 + l + sovGenerated(uint64(l))
	if m.LabelSelectorPath != nil {
		l = len(*m.LabelSelectorPath)
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *CustomResourceSubresourceStatus) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *CustomResourceSubresources) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Status != nil {
		l = m.Status.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.Scale != nil {
		l = m.Scale.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *CustomResourceValidation) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.OpenAPIV3Schema != nil {
		l = m.OpenAPIV3Schema.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *ExternalDocumentation) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Description)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.URL)
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func (m *JSON) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Raw != nil {
		l = len(m.Raw)
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *JSONSchemaProps) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ID)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Schema)
	n += 1 + l + sovGenerated(uint64(l))
	if m.Ref != nil {
		l = len(*m.Ref)
		n += 1 + l + sovGenerated(uint64(l))
	}
	l = len(m.Description)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Type)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Format)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Title)
	n += 1 + l + sovGenerated(uint64(l))
	if m.Default != nil {
		l = m.Default.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.Maximum != nil {
		n += 9
	}
	n += 2
	if m.Minimum != nil {
		n += 9
	}
	n += 2
	if m.MaxLength != nil {
		n += 1 + sovGenerated(uint64(*m.MaxLength))
	}
	if m.MinLength != nil {
		n += 1 + sovGenerated(uint64(*m.MinLength))
	}
	l = len(m.Pattern)
	n += 1 + l + sovGenerated(uint64(l))
	if m.MaxItems != nil {
		n += 2 + sovGenerated(uint64(*m.MaxItems))
	}
	if m.MinItems != nil {
		n += 2 + sovGenerated(uint64(*m.MinItems))
	}
	n += 3
	if m.MultipleOf != nil {
		n += 10
	}
	if len(m.Enum) > 0 {
		for _, e := range m.Enum {
			l = e.Size()
			n += 2 + l + sovGenerated(uint64(l))
		}
	}
	if m.MaxProperties != nil {
		n += 2 + sovGenerated(uint64(*m.MaxProperties))
	}
	if m.MinProperties != nil {
		n += 2 + sovGenerated(uint64(*m.MinProperties))
	}
	if len(m.Required) > 0 {
		for _, s := range m.Required {
			l = len(s)
			n += 2 + l + sovGenerated(uint64(l))
		}
	}
	if m.Items != nil {
		l = m.Items.Size()
		n += 2 + l + sovGenerated(uint64(l))
	}
	if len(m.AllOf) > 0 {
		for _, e := range m.AllOf {
			l = e.Size()
			n += 2 + l + sovGenerated(uint64(l))
		}
	}
	if len(m.OneOf) > 0 {
		for _, e := range m.OneOf {
			l = e.Size()
			n += 2 + l + sovGenerated(uint64(l))
		}
	}
	if len(m.AnyOf) > 0 {
		for _, e := range m.AnyOf {
			l = e.Size()
			n += 2 + l + sovGenerated(uint64(l))
		}
	}
	if m.Not != nil {
		l = m.Not.Size()
		n += 2 + l + sovGenerated(uint64(l))
	}
	if len(m.Properties) > 0 {
		for k, v := range m.Properties {
			_ = k
			_ = v
			l = v.Size()
			mapEntrySize := 1 + len(k) + sovGenerated(uint64(len(k))) + 1 + l + sovGenerated(uint64(l))
			n += mapEntrySize + 2 + sovGenerated(uint64(mapEntrySize))
		}
	}
	if m.AdditionalProperties != nil {
		l = m.AdditionalProperties.Size()
		n += 2 + l + sovGenerated(uint64(l))
	}
	if len(m.PatternProperties) > 0 {
		for k, v := range m.PatternProperties {
			_ = k
			_ = v
			l = v.Size()
			mapEntrySize := 1 + len(k) + sovGenerated(uint64(len(k))) + 1 + l + sovGenerated(uint64(l))
			n += mapEntrySize + 2 + sovGenerated(uint64(mapEntrySize))
		}
	}
	if len(m.Dependencies) > 0 {
		for k, v := range m.Dependencies {
			_ = k
			_ = v
			l = v.Size()
			mapEntrySize := 1 + len(k) + sovGenerated(uint64(len(k))) + 1 + l + sovGenerated(uint64(l))
			n += mapEntrySize + 2 + sovGenerated(uint64(mapEntrySize))
		}
	}
	if m.AdditionalItems != nil {
		l = m.AdditionalItems.Size()
		n += 2 + l + sovGenerated(uint64(l))
	}
	if len(m.Definitions) > 0 {
		for k, v := range m.Definitions {
			_ = k
			_ = v
			l = v.Size()
			mapEntrySize := 1 + len(k) + sovGenerated(uint64(len(k))) + 1 + l + sovGenerated(uint64(l))
			n += mapEntrySize + 2 + sovGenerated(uint64(mapEntrySize))
		}
	}
	if m.ExternalDocs != nil {
		l = m.ExternalDocs.Size()
		n += 2 + l + sovGenerated(uint64(l))
	}
	if m.Example != nil {
		l = m.Example.Size()
		n += 2 + l + sovGenerated(uint64(l))
	}
	n += 3
	if m.XPreserveUnknownFields != nil {
		n += 3
	}
	n += 3
	n += 3
	if len(m.XListMapKeys) > 0 {
		for _, s := range m.XListMapKeys {
			l = len(s)
			n += 2 + l + sovGenerated(uint64(l))
		}
	}
	if m.XListType != nil {
		l = len(*m.XListType)
		n += 2 + l + sovGenerated(uint64(l))
	}
	if m.XMapType != nil {
		l = len(*m.XMapType)
		n += 2 + l + sovGenerated(uint64(l))
	}
	if len(m.XValidations) > 0 {
		for _, e := range m.XValidations {
			l = e.Size()
			n += 2 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *JSONSchemaPropsOrArray) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Schema != nil {
		l = m.Schema.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	if len(m.JSONSchemas) > 0 {
		for _, e := range m.JSONSchemas {
			l = e.Size()
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *JSONSchemaPropsOrBool) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 2
	if m.Schema != nil {
		l = m.Schema.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *JSONSchemaPropsOrStringArray) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Schema != nil {
		l = m.Schema.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	if len(m.Property) > 0 {
		for _, s := range m.Property {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func (m *SelectableField) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.JSONPath)
	n += 1 + l + sovGenerated(uint64(l))
	return n
}

func (m *ServiceReference) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Namespace)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovGenerated(uint64(l))
	if m.Path != nil {
		l = len(*m.Path)
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.Port != nil {
		n += 1 + sovGenerated(uint64(*m.Port))
	}
	return n
}

func (m *ValidationRule) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Rule)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.Message)
	n += 1 + l + sovGenerated(uint64(l))
	l = len(m.MessageExpression)
	n += 1 + l + sovGenerated(uint64(l))
	if m.Reason != nil {
		l = len(*m.Reason)
		n += 1 + l + sovGenerated(uint64(l))
	}
	l = len(m.FieldPath)
	n += 1 + l + sovGenerated(uint64(l))
	if m.OptionalOldSelf != nil {
		n += 2
	}
	return n
}

func (m *WebhookClientConfig) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Service != nil {
		l = m.Service.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.CABundle != nil {
		l = len(m.CABundle)
		n += 1 + l + sovGenerated(uint64(l))
	}
	if m.URL != nil {
		l = len(*m.URL)
		n += 1 + l + sovGenerated(uint64(l))
	}
	return n
}

func (m *WebhookConversion) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ClientConfig != nil {
		l = m.ClientConfig.Size()
		n += 1 + l + sovGenerated(uint64(l))
	}
	if len(m.ConversionReviewVersions) > 0 {
		for _, s := range m.ConversionReviewVersions {
			l = len(s)
			n += 1 + l + sovGenerated(uint64(l))
		}
	}
	return n
}

func sovGenerated(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozGenerated(x uint64) (n int) {
	return sovGenerated(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *ConversionRequest) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForObjects := "[]RawExtension{"
	for _, f := range this.Objects {
		repeatedStringForObjects += fmt.Sprintf("%v", f) + ","
	}
	repeatedStringForObjects += "}"
	s := strings.Join([]string{`&ConversionRequest{`,
		`UID:` + fmt.Sprintf("%v", this.UID) + `,`,
		`DesiredAPIVersion:` + fmt.Sprintf("%v", this.DesiredAPIVersion) + `,`,
		`Objects:` + repeatedStringForObjects + `,`,
		`}`,
	}, "")
	return s
}
func (this *ConversionResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForConvertedObjects := "[]RawExtension{"
	for _, f := range this.ConvertedObjects {
		repeatedStringForConvertedObjects += fmt.Sprintf("%v", f) + ","
	}
	repeatedStringForConvertedObjects += "}"
	s := strings.Join([]string{`&ConversionResponse{`,
		`UID:` + fmt.Sprintf("%v", this.UID) + `,`,
		`ConvertedObjects:` + repeatedStringForConvertedObjects + `,`,
		`Result:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.Result), "Status", "v1.Status", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ConversionReview) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ConversionReview{`,
		`Request:` + strings.Replace(this.Request.String(), "ConversionRequest", "ConversionRequest", 1) + `,`,
		`Response:` + strings.Replace(this.Response.String(), "ConversionResponse", "ConversionResponse", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CustomResourceColumnDefinition) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CustomResourceColumnDefinition{`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`Type:` + fmt.Sprintf("%v", this.Type) + `,`,
		`Format:` + fmt.Sprintf("%v", this.Format) + `,`,
		`Description:` + fmt.Sprintf("%v", this.Description) + `,`,
		`Priority:` + fmt.Sprintf("%v", this.Priority) + `,`,
		`JSONPath:` + fmt.Sprintf("%v", this.JSONPath) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CustomResourceConversion) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CustomResourceConversion{`,
		`Strategy:` + fmt.Sprintf("%v", this.Strategy) + `,`,
		`Webhook:` + strings.Replace(this.Webhook.String(), "WebhookConversion", "WebhookConversion", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CustomResourceDefinition) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CustomResourceDefinition{`,
		`ObjectMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ObjectMeta), "ObjectMeta", "v1.ObjectMeta", 1), `&`, ``, 1) + `,`,
		`Spec:` + strings.Replace(strings.Replace(this.Spec.String(), "CustomResourceDefinitionSpec", "CustomResourceDefinitionSpec", 1), `&`, ``, 1) + `,`,
		`Status:` + strings.Replace(strings.Replace(this.Status.String(), "CustomResourceDefinitionStatus", "CustomResourceDefinitionStatus", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CustomResourceDefinitionCondition) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CustomResourceDefinitionCondition{`,
		`Type:` + fmt.Sprintf("%v", this.Type) + `,`,
		`Status:` + fmt.Sprintf("%v", this.Status) + `,`,
		`LastTransitionTime:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.LastTransitionTime), "Time", "v1.Time", 1), `&`, ``, 1) + `,`,
		`Reason:` + fmt.Sprintf("%v", this.Reason) + `,`,
		`Message:` + fmt.Sprintf("%v", this.Message) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CustomResourceDefinitionList) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForItems := "[]CustomResourceDefinition{"
	for _, f := range this.Items {
		repeatedStringForItems += strings.Replace(strings.Replace(f.String(), "CustomResourceDefinition", "CustomResourceDefinition", 1), `&`, ``, 1) + ","
	}
	repeatedStringForItems += "}"
	s := strings.Join([]string{`&CustomResourceDefinitionList{`,
		`ListMeta:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.ListMeta), "ListMeta", "v1.ListMeta", 1), `&`, ``, 1) + `,`,
		`Items:` + repeatedStringForItems + `,`,
		`}`,
	}, "")
	return s
}
func (this *CustomResourceDefinitionNames) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CustomResourceDefinitionNames{`,
		`Plural:` + fmt.Sprintf("%v", this.Plural) + `,`,
		`Singular:` + fmt.Sprintf("%v", this.Singular) + `,`,
		`ShortNames:` + fmt.Sprintf("%v", this.ShortNames) + `,`,
		`Kind:` + fmt.Sprintf("%v", this.Kind) + `,`,
		`ListKind:` + fmt.Sprintf("%v", this.ListKind) + `,`,
		`Categories:` + fmt.Sprintf("%v", this.Categories) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CustomResourceDefinitionSpec) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForVersions := "[]CustomResourceDefinitionVersion{"
	for _, f := range this.Versions {
		repeatedStringForVersions += strings.Replace(strings.Replace(f.String(), "CustomResourceDefinitionVersion", "CustomResourceDefinitionVersion", 1), `&`, ``, 1) + ","
	}
	repeatedStringForVersions += "}"
	s := strings.Join([]string{`&CustomResourceDefinitionSpec{`,
		`Group:` + fmt.Sprintf("%v", this.Group) + `,`,
		`Names:` + strings.Replace(strings.Replace(this.Names.String(), "CustomResourceDefinitionNames", "CustomResourceDefinitionNames", 1), `&`, ``, 1) + `,`,
		`Scope:` + fmt.Sprintf("%v", this.Scope) + `,`,
		`Versions:` + repeatedStringForVersions + `,`,
		`Conversion:` + strings.Replace(this.Conversion.String(), "CustomResourceConversion", "CustomResourceConversion", 1) + `,`,
		`PreserveUnknownFields:` + fmt.Sprintf("%v", this.PreserveUnknownFields) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CustomResourceDefinitionStatus) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForConditions := "[]CustomResourceDefinitionCondition{"
	for _, f := range this.Conditions {
		repeatedStringForConditions += strings.Replace(strings.Replace(f.String(), "CustomResourceDefinitionCondition", "CustomResourceDefinitionCondition", 1), `&`, ``, 1) + ","
	}
	repeatedStringForConditions += "}"
	s := strings.Join([]string{`&CustomResourceDefinitionStatus{`,
		`Conditions:` + repeatedStringForConditions + `,`,
		`AcceptedNames:` + strings.Replace(strings.Replace(this.AcceptedNames.String(), "CustomResourceDefinitionNames", "CustomResourceDefinitionNames", 1), `&`, ``, 1) + `,`,
		`StoredVersions:` + fmt.Sprintf("%v", this.StoredVersions) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CustomResourceDefinitionVersion) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForAdditionalPrinterColumns := "[]CustomResourceColumnDefinition{"
	for _, f := range this.AdditionalPrinterColumns {
		repeatedStringForAdditionalPrinterColumns += strings.Replace(strings.Replace(f.String(), "CustomResourceColumnDefinition", "CustomResourceColumnDefinition", 1), `&`, ``, 1) + ","
	}
	repeatedStringForAdditionalPrinterColumns += "}"
	repeatedStringForSelectableFields := "[]SelectableField{"
	for _, f := range this.SelectableFields {
		repeatedStringForSelectableFields += strings.Replace(strings.Replace(f.String(), "SelectableField", "SelectableField", 1), `&`, ``, 1) + ","
	}
	repeatedStringForSelectableFields += "}"
	s := strings.Join([]string{`&CustomResourceDefinitionVersion{`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`Served:` + fmt.Sprintf("%v", this.Served) + `,`,
		`Storage:` + fmt.Sprintf("%v", this.Storage) + `,`,
		`Schema:` + strings.Replace(this.Schema.String(), "CustomResourceValidation", "CustomResourceValidation", 1) + `,`,
		`Subresources:` + strings.Replace(this.Subresources.String(), "CustomResourceSubresources", "CustomResourceSubresources", 1) + `,`,
		`AdditionalPrinterColumns:` + repeatedStringForAdditionalPrinterColumns + `,`,
		`Deprecated:` + fmt.Sprintf("%v", this.Deprecated) + `,`,
		`DeprecationWarning:` + valueToStringGenerated(this.DeprecationWarning) + `,`,
		`SelectableFields:` + repeatedStringForSelectableFields + `,`,
		`}`,
	}, "")
	return s
}
func (this *CustomResourceSubresourceScale) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CustomResourceSubresourceScale{`,
		`SpecReplicasPath:` + fmt.Sprintf("%v", this.SpecReplicasPath) + `,`,
		`StatusReplicasPath:` + fmt.Sprintf("%v", this.StatusReplicasPath) + `,`,
		`LabelSelectorPath:` + valueToStringGenerated(this.LabelSelectorPath) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CustomResourceSubresourceStatus) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CustomResourceSubresourceStatus{`,
		`}`,
	}, "")
	return s
}
func (this *CustomResourceSubresources) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CustomResourceSubresources{`,
		`Status:` + strings.Replace(this.Status.String(), "CustomResourceSubresourceStatus", "CustomResourceSubresourceStatus", 1) + `,`,
		`Scale:` + strings.Replace(this.Scale.String(), "CustomResourceSubresourceScale", "CustomResourceSubresourceScale", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CustomResourceValidation) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CustomResourceValidation{`,
		`OpenAPIV3Schema:` + strings.Replace(this.OpenAPIV3Schema.String(), "JSONSchemaProps", "JSONSchemaProps", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ExternalDocumentation) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ExternalDocumentation{`,
		`Description:` + fmt.Sprintf("%v", this.Description) + `,`,
		`URL:` + fmt.Sprintf("%v", this.URL) + `,`,
		`}`,
	}, "")
	return s
}
func (this *JSON) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&JSON{`,
		`Raw:` + valueToStringGenerated(this.Raw) + `,`,
		`}`,
	}, "")
	return s
}
func (this *JSONSchemaProps) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForEnum := "[]JSON{"
	for _, f := range this.Enum {
		repeatedStringForEnum += strings.Replace(strings.Replace(f.String(), "JSON", "JSON", 1), `&`, ``, 1) + ","
	}
	repeatedStringForEnum += "}"
	repeatedStringForAllOf := "[]JSONSchemaProps{"
	for _, f := range this.AllOf {
		repeatedStringForAllOf += strings.Replace(strings.Replace(f.String(), "JSONSchemaProps", "JSONSchemaProps", 1), `&`, ``, 1) + ","
	}
	repeatedStringForAllOf += "}"
	repeatedStringForOneOf := "[]JSONSchemaProps{"
	for _, f := range this.OneOf {
		repeatedStringForOneOf += strings.Replace(strings.Replace(f.String(), "JSONSchemaProps", "JSONSchemaProps", 1), `&`, ``, 1) + ","
	}
	repeatedStringForOneOf += "}"
	repeatedStringForAnyOf := "[]JSONSchemaProps{"
	for _, f := range this.AnyOf {
		repeatedStringForAnyOf += strings.Replace(strings.Replace(f.String(), "JSONSchemaProps", "JSONSchemaProps", 1), `&`, ``, 1) + ","
	}
	repeatedStringForAnyOf += "}"
	repeatedStringForXValidations := "[]ValidationRule{"
	for _, f := range this.XValidations {
		repeatedStringForXValidations += strings.Replace(strings.Replace(f.String(), "ValidationRule", "ValidationRule", 1), `&`, ``, 1) + ","
	}
	repeatedStringForXValidations += "}"
	keysForProperties := make([]string, 0, len(this.Properties))
	for k := range this.Properties {
		keysForProperties = append(keysForProperties, k)
	}
	github_com_gogo_protobuf_sortkeys.Strings(keysForProperties)
	mapStringForProperties := "map[string]JSONSchemaProps{"
	for _, k := range keysForProperties {
		mapStringForProperties += fmt.Sprintf("%v: %v,", k, this.Properties[k])
	}
	mapStringForProperties += "}"
	keysForPatternProperties := make([]string, 0, len(this.PatternProperties))
	for k := range this.PatternProperties {
		keysForPatternProperties = append(keysForPatternProperties, k)
	}
	github_com_gogo_protobuf_sortkeys.Strings(keysForPatternProperties)
	mapStringForPatternProperties := "map[string]JSONSchemaProps{"
	for _, k := range keysForPatternProperties {
		mapStringForPatternProperties += fmt.Sprintf("%v: %v,", k, this.PatternProperties[k])
	}
	mapStringForPatternProperties += "}"
	keysForDependencies := make([]string, 0, len(this.Dependencies))
	for k := range this.Dependencies {
		keysForDependencies = append(keysForDependencies, k)
	}
	github_com_gogo_protobuf_sortkeys.Strings(keysForDependencies)
	mapStringForDependencies := "JSONSchemaDependencies{"
	for _, k := range keysForDependencies {
		mapStringForDependencies += fmt.Sprintf("%v: %v,", k, this.Dependencies[k])
	}
	mapStringForDependencies += "}"
	keysForDefinitions := make([]string, 0, len(this.Definitions))
	for k := range this.Definitions {
		keysForDefinitions = append(keysForDefinitions, k)
	}
	github_com_gogo_protobuf_sortkeys.Strings(keysForDefinitions)
	mapStringForDefinitions := "JSONSchemaDefinitions{"
	for _, k := range keysForDefinitions {
		mapStringForDefinitions += fmt.Sprintf("%v: %v,", k, this.Definitions[k])
	}
	mapStringForDefinitions += "}"
	s := strings.Join([]string{`&JSONSchemaProps{`,
		`ID:` + fmt.Sprintf("%v", this.ID) + `,`,
		`Schema:` + fmt.Sprintf("%v", this.Schema) + `,`,
		`Ref:` + valueToStringGenerated(this.Ref) + `,`,
		`Description:` + fmt.Sprintf("%v", this.Description) + `,`,
		`Type:` + fmt.Sprintf("%v", this.Type) + `,`,
		`Format:` + fmt.Sprintf("%v", this.Format) + `,`,
		`Title:` + fmt.Sprintf("%v", this.Title) + `,`,
		`Default:` + strings.Replace(this.Default.String(), "JSON", "JSON", 1) + `,`,
		`Maximum:` + valueToStringGenerated(this.Maximum) + `,`,
		`ExclusiveMaximum:` + fmt.Sprintf("%v", this.ExclusiveMaximum) + `,`,
		`Minimum:` + valueToStringGenerated(this.Minimum) + `,`,
		`ExclusiveMinimum:` + fmt.Sprintf("%v", this.ExclusiveMinimum) + `,`,
		`MaxLength:` + valueToStringGenerated(this.MaxLength) + `,`,
		`MinLength:` + valueToStringGenerated(this.MinLength) + `,`,
		`Pattern:` + fmt.Sprintf("%v", this.Pattern) + `,`,
		`MaxItems:` + valueToStringGenerated(this.MaxItems) + `,`,
		`MinItems:` + valueToStringGenerated(this.MinItems) + `,`,
		`UniqueItems:` + fmt.Sprintf("%v", this.UniqueItems) + `,`,
		`MultipleOf:` + valueToStringGenerated(this.MultipleOf) + `,`,
		`Enum:` + repeatedStringForEnum + `,`,
		`MaxProperties:` + valueToStringGenerated(this.MaxProperties) + `,`,
		`MinProperties:` + valueToStringGenerated(this.MinProperties) + `,`,
		`Required:` + fmt.Sprintf("%v", this.Required) + `,`,
		`Items:` + strings.Replace(this.Items.String(), "JSONSchemaPropsOrArray", "JSONSchemaPropsOrArray", 1) + `,`,
		`AllOf:` + repeatedStringForAllOf + `,`,
		`OneOf:` + repeatedStringForOneOf + `,`,
		`AnyOf:` + repeatedStringForAnyOf + `,`,
		`Not:` + strings.Replace(this.Not.String(), "JSONSchemaProps", "JSONSchemaProps", 1) + `,`,
		`Properties:` + mapStringForProperties + `,`,
		`AdditionalProperties:` + strings.Replace(this.AdditionalProperties.String(), "JSONSchemaPropsOrBool", "JSONSchemaPropsOrBool", 1) + `,`,
		`PatternProperties:` + mapStringForPatternProperties + `,`,
		`Dependencies:` + mapStringForDependencies + `,`,
		`AdditionalItems:` + strings.Replace(this.AdditionalItems.String(), "JSONSchemaPropsOrBool", "JSONSchemaPropsOrBool", 1) + `,`,
		`Definitions:` + mapStringForDefinitions + `,`,
		`ExternalDocs:` + strings.Replace(this.ExternalDocs.String(), "ExternalDocumentation", "ExternalDocumentation", 1) + `,`,
		`Example:` + strings.Replace(this.Example.String(), "JSON", "JSON", 1) + `,`,
		`Nullable:` + fmt.Sprintf("%v", this.Nullable) + `,`,
		`XPreserveUnknownFields:` + valueToStringGenerated(this.XPreserveUnknownFields) + `,`,
		`XEmbeddedResource:` + fmt.Sprintf("%v", this.XEmbeddedResource) + `,`,
		`XIntOrString:` + fmt.Sprintf("%v", this.XIntOrString) + `,`,
		`XListMapKeys:` + fmt.Sprintf("%v", this.XListMapKeys) + `,`,
		`XListType:` + valueToStringGenerated(this.XListType) + `,`,
		`XMapType:` + valueToStringGenerated(this.XMapType) + `,`,
		`XValidations:` + repeatedStringForXValidations + `,`,
		`}`,
	}, "")
	return s
}
func (this *JSONSchemaPropsOrArray) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForJSONSchemas := "[]JSONSchemaProps{"
	for _, f := range this.JSONSchemas {
		repeatedStringForJSONSchemas += strings.Replace(strings.Replace(f.String(), "JSONSchemaProps", "JSONSchemaProps", 1), `&`, ``, 1) + ","
	}
	repeatedStringForJSONSchemas += "}"
	s := strings.Join([]string{`&JSONSchemaPropsOrArray{`,
		`Schema:` + strings.Replace(this.Schema.String(), "JSONSchemaProps", "JSONSchemaProps", 1) + `,`,
		`JSONSchemas:` + repeatedStringForJSONSchemas + `,`,
		`}`,
	}, "")
	return s
}
func (this *JSONSchemaPropsOrBool) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&JSONSchemaPropsOrBool{`,
		`Allows:` + fmt.Sprintf("%v", this.Allows) + `,`,
		`Schema:` + strings.Replace(this.Schema.String(), "JSONSchemaProps", "JSONSchemaProps", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *JSONSchemaPropsOrStringArray) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&JSONSchemaPropsOrStringArray{`,
		`Schema:` + strings.Replace(this.Schema.String(), "JSONSchemaProps", "JSONSchemaProps", 1) + `,`,
		`Property:` + fmt.Sprintf("%v", this.Property) + `,`,
		`}`,
	}, "")
	return s
}
func (this *SelectableField) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&SelectableField{`,
		`JSONPath:` + fmt.Sprintf("%v", this.JSONPath) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ServiceReference) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ServiceReference{`,
		`Namespace:` + fmt.Sprintf("%v", this.Namespace) + `,`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`Path:` + valueToStringGenerated(this.Path) + `,`,
		`Port:` + valueToStringGenerated(this.Port) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ValidationRule) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ValidationRule{`,
		`Rule:` + fmt.Sprintf("%v", this.Rule) + `,`,
		`Message:` + fmt.Sprintf("%v", this.Message) + `,`,
		`MessageExpression:` + fmt.Sprintf("%v", this.MessageExpression) + `,`,
		`Reason:` + valueToStringGenerated(this.Reason) + `,`,
		`FieldPath:` + fmt.Sprintf("%v", this.FieldPath) + `,`,
		`OptionalOldSelf:` + valueToStringGenerated(this.OptionalOldSelf) + `,`,
		`}`,
	}, "")
	return s
}
func (this *WebhookClientConfig) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&WebhookClientConfig{`,
		`Service:` + strings.Replace(this.Service.String(), "ServiceReference", "ServiceReference", 1) + `,`,
		`CABundle:` + valueToStringGenerated(this.CABundle) + `,`,
		`URL:` + valueToStringGenerated(this.URL) + `,`,
		`}`,
	}, "")
	return s
}
func (this *WebhookConversion) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&WebhookConversion{`,
		`ClientConfig:` + strings.Replace(this.ClientConfig.String(), "WebhookClientConfig", "WebhookClientConfig", 1) + `,`,
		`ConversionReviewVersions:` + fmt.Sprintf("%v", this.ConversionReviewVersions) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringGenerated(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *ConversionRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ConversionRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ConversionRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UID = k8s_io_apimachinery_pkg_types.UID(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DesiredAPIVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DesiredAPIVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Objects", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Objects = append(m.Objects, runtime.RawExtension{})
			if err := m.Objects[len(m.Objects)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConversionResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ConversionResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ConversionResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UID = k8s_io_apimachinery_pkg_types.UID(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConvertedObjects", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ConvertedObjects = append(m.ConvertedObjects, runtime.RawExtension{})
			if err := m.ConvertedObjects[len(m.ConvertedObjects)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Result.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConversionReview) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ConversionReview: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ConversionReview: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Request", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Request == nil {
				m.Request = &ConversionRequest{}
			}
			if err := m.Request.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Response", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Response == nil {
				m.Response = &ConversionResponse{}
			}
			if err := m.Response.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CustomResourceColumnDefinition) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CustomResourceColumnDefinition: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CustomResourceColumnDefinition: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Format", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Format = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Priority", wireType)
			}
			m.Priority = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Priority |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field JSONPath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.JSONPath = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CustomResourceConversion) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CustomResourceConversion: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CustomResourceConversion: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Strategy", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Strategy = ConversionStrategyType(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Webhook", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Webhook == nil {
				m.Webhook = &WebhookConversion{}
			}
			if err := m.Webhook.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CustomResourceDefinition) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CustomResourceDefinition: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CustomResourceDefinition: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ObjectMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ObjectMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Spec", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Spec.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Status.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CustomResourceDefinitionCondition) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CustomResourceDefinitionCondition: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CustomResourceDefinitionCondition: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = CustomResourceDefinitionConditionType(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Status = ConditionStatus(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastTransitionTime", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.LastTransitionTime.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CustomResourceDefinitionList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CustomResourceDefinitionList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CustomResourceDefinitionList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ListMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.ListMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Items", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Items = append(m.Items, CustomResourceDefinition{})
			if err := m.Items[len(m.Items)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CustomResourceDefinitionNames) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CustomResourceDefinitionNames: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CustomResourceDefinitionNames: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Plural", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Plural = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Singular", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Singular = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShortNames", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ShortNames = append(m.ShortNames, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Kind", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Kind = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ListKind", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ListKind = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Categories", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Categories = append(m.Categories, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CustomResourceDefinitionSpec) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CustomResourceDefinitionSpec: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CustomResourceDefinitionSpec: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Group", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Group = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Names", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Names.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Scope", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Scope = ResourceScope(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Versions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Versions = append(m.Versions, CustomResourceDefinitionVersion{})
			if err := m.Versions[len(m.Versions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Conversion", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Conversion == nil {
				m.Conversion = &CustomResourceConversion{}
			}
			if err := m.Conversion.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PreserveUnknownFields", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PreserveUnknownFields = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CustomResourceDefinitionStatus) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CustomResourceDefinitionStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CustomResourceDefinitionStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Conditions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Conditions = append(m.Conditions, CustomResourceDefinitionCondition{})
			if err := m.Conditions[len(m.Conditions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AcceptedNames", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.AcceptedNames.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoredVersions", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StoredVersions = append(m.StoredVersions, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CustomResourceDefinitionVersion) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CustomResourceDefinitionVersion: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CustomResourceDefinitionVersion: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Served", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Served = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Storage", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Storage = bool(v != 0)
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Schema", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Schema == nil {
				m.Schema = &CustomResourceValidation{}
			}
			if err := m.Schema.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Subresources", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Subresources == nil {
				m.Subresources = &CustomResourceSubresources{}
			}
			if err := m.Subresources.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdditionalPrinterColumns", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AdditionalPrinterColumns = append(m.AdditionalPrinterColumns, CustomResourceColumnDefinition{})
			if err := m.AdditionalPrinterColumns[len(m.AdditionalPrinterColumns)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Deprecated", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Deprecated = bool(v != 0)
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeprecationWarning", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.DeprecationWarning = &s
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SelectableFields", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SelectableFields = append(m.SelectableFields, SelectableField{})
			if err := m.SelectableFields[len(m.SelectableFields)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CustomResourceSubresourceScale) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CustomResourceSubresourceScale: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CustomResourceSubresourceScale: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpecReplicasPath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SpecReplicasPath = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StatusReplicasPath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StatusReplicasPath = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LabelSelectorPath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.LabelSelectorPath = &s
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CustomResourceSubresourceStatus) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CustomResourceSubresourceStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CustomResourceSubresourceStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CustomResourceSubresources) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CustomResourceSubresources: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CustomResourceSubresources: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Status == nil {
				m.Status = &CustomResourceSubresourceStatus{}
			}
			if err := m.Status.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Scale", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Scale == nil {
				m.Scale = &CustomResourceSubresourceScale{}
			}
			if err := m.Scale.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CustomResourceValidation) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CustomResourceValidation: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CustomResourceValidation: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OpenAPIV3Schema", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.OpenAPIV3Schema == nil {
				m.OpenAPIV3Schema = &JSONSchemaProps{}
			}
			if err := m.OpenAPIV3Schema.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExternalDocumentation) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ExternalDocumentation: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ExternalDocumentation: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field URL", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.URL = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *JSON) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: JSON: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: JSON: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Raw", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Raw = append(m.Raw[:0], dAtA[iNdEx:postIndex]...)
			if m.Raw == nil {
				m.Raw = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *JSONSchemaProps) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: JSONSchemaProps: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: JSONSchemaProps: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Schema", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Schema = JSONSchemaURL(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ref", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Ref = &s
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Format", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Format = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Default", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Default == nil {
				m.Default = &JSON{}
			}
			if err := m.Default.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Maximum", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			v2 := float64(math.Float64frombits(v))
			m.Maximum = &v2
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExclusiveMaximum", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ExclusiveMaximum = bool(v != 0)
		case 11:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Minimum", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			v2 := float64(math.Float64frombits(v))
			m.Minimum = &v2
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExclusiveMinimum", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ExclusiveMinimum = bool(v != 0)
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxLength", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.MaxLength = &v
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MinLength", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.MinLength = &v
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Pattern", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Pattern = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 16:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxItems", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.MaxItems = &v
		case 17:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MinItems", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.MinItems = &v
		case 18:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UniqueItems", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.UniqueItems = bool(v != 0)
		case 19:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field MultipleOf", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			v2 := float64(math.Float64frombits(v))
			m.MultipleOf = &v2
		case 20:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Enum", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Enum = append(m.Enum, JSON{})
			if err := m.Enum[len(m.Enum)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 21:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxProperties", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.MaxProperties = &v
		case 22:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MinProperties", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.MinProperties = &v
		case 23:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Required", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Required = append(m.Required, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 24:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Items", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Items == nil {
				m.Items = &JSONSchemaPropsOrArray{}
			}
			if err := m.Items.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 25:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AllOf", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AllOf = append(m.AllOf, JSONSchemaProps{})
			if err := m.AllOf[len(m.AllOf)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 26:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OneOf", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OneOf = append(m.OneOf, JSONSchemaProps{})
			if err := m.OneOf[len(m.OneOf)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 27:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AnyOf", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AnyOf = append(m.AnyOf, JSONSchemaProps{})
			if err := m.AnyOf[len(m.AnyOf)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 28:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Not", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Not == nil {
				m.Not = &JSONSchemaProps{}
			}
			if err := m.Not.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 29:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Properties", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Properties == nil {
				m.Properties = make(map[string]JSONSchemaProps)
			}
			var mapkey string
			mapvalue := &JSONSchemaProps{}
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGenerated
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var mapmsglen int
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapmsglen |= int(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					if mapmsglen < 0 {
						return ErrInvalidLengthGenerated
					}
					postmsgIndex := iNdEx + mapmsglen
					if postmsgIndex < 0 {
						return ErrInvalidLengthGenerated
					}
					if postmsgIndex > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = &JSONSchemaProps{}
					if err := mapvalue.Unmarshal(dAtA[iNdEx:postmsgIndex]); err != nil {
						return err
					}
					iNdEx = postmsgIndex
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipGenerated(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthGenerated
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Properties[mapkey] = *mapvalue
			iNdEx = postIndex
		case 30:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdditionalProperties", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AdditionalProperties == nil {
				m.AdditionalProperties = &JSONSchemaPropsOrBool{}
			}
			if err := m.AdditionalProperties.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 31:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PatternProperties", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.PatternProperties == nil {
				m.PatternProperties = make(map[string]JSONSchemaProps)
			}
			var mapkey string
			mapvalue := &JSONSchemaProps{}
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGenerated
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var mapmsglen int
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapmsglen |= int(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					if mapmsglen < 0 {
						return ErrInvalidLengthGenerated
					}
					postmsgIndex := iNdEx + mapmsglen
					if postmsgIndex < 0 {
						return ErrInvalidLengthGenerated
					}
					if postmsgIndex > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = &JSONSchemaProps{}
					if err := mapvalue.Unmarshal(dAtA[iNdEx:postmsgIndex]); err != nil {
						return err
					}
					iNdEx = postmsgIndex
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipGenerated(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthGenerated
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.PatternProperties[mapkey] = *mapvalue
			iNdEx = postIndex
		case 32:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dependencies", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Dependencies == nil {
				m.Dependencies = make(JSONSchemaDependencies)
			}
			var mapkey string
			mapvalue := &JSONSchemaPropsOrStringArray{}
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGenerated
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var mapmsglen int
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapmsglen |= int(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					if mapmsglen < 0 {
						return ErrInvalidLengthGenerated
					}
					postmsgIndex := iNdEx + mapmsglen
					if postmsgIndex < 0 {
						return ErrInvalidLengthGenerated
					}
					if postmsgIndex > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = &JSONSchemaPropsOrStringArray{}
					if err := mapvalue.Unmarshal(dAtA[iNdEx:postmsgIndex]); err != nil {
						return err
					}
					iNdEx = postmsgIndex
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipGenerated(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthGenerated
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Dependencies[mapkey] = *mapvalue
			iNdEx = postIndex
		case 33:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdditionalItems", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AdditionalItems == nil {
				m.AdditionalItems = &JSONSchemaPropsOrBool{}
			}
			if err := m.AdditionalItems.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 34:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Definitions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Definitions == nil {
				m.Definitions = make(JSONSchemaDefinitions)
			}
			var mapkey string
			mapvalue := &JSONSchemaProps{}
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGenerated
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthGenerated
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var mapmsglen int
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGenerated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapmsglen |= int(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					if mapmsglen < 0 {
						return ErrInvalidLengthGenerated
					}
					postmsgIndex := iNdEx + mapmsglen
					if postmsgIndex < 0 {
						return ErrInvalidLengthGenerated
					}
					if postmsgIndex > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = &JSONSchemaProps{}
					if err := mapvalue.Unmarshal(dAtA[iNdEx:postmsgIndex]); err != nil {
						return err
					}
					iNdEx = postmsgIndex
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipGenerated(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthGenerated
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Definitions[mapkey] = *mapvalue
			iNdEx = postIndex
		case 35:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExternalDocs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ExternalDocs == nil {
				m.ExternalDocs = &ExternalDocumentation{}
			}
			if err := m.ExternalDocs.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 36:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Example", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Example == nil {
				m.Example = &JSON{}
			}
			if err := m.Example.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 37:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Nullable", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Nullable = bool(v != 0)
		case 38:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field XPreserveUnknownFields", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.XPreserveUnknownFields = &b
		case 39:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field XEmbeddedResource", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.XEmbeddedResource = bool(v != 0)
		case 40:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field XIntOrString", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.XIntOrString = bool(v != 0)
		case 41:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field XListMapKeys", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.XListMapKeys = append(m.XListMapKeys, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 42:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field XListType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.XListType = &s
			iNdEx = postIndex
		case 43:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field XMapType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.XMapType = &s
			iNdEx = postIndex
		case 44:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field XValidations", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.XValidations = append(m.XValidations, ValidationRule{})
			if err := m.XValidations[len(m.XValidations)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *JSONSchemaPropsOrArray) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: JSONSchemaPropsOrArray: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: JSONSchemaPropsOrArray: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Schema", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Schema == nil {
				m.Schema = &JSONSchemaProps{}
			}
			if err := m.Schema.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field JSONSchemas", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.JSONSchemas = append(m.JSONSchemas, JSONSchemaProps{})
			if err := m.JSONSchemas[len(m.JSONSchemas)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *JSONSchemaPropsOrBool) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: JSONSchemaPropsOrBool: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: JSONSchemaPropsOrBool: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Allows", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Allows = bool(v != 0)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Schema", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Schema == nil {
				m.Schema = &JSONSchemaProps{}
			}
			if err := m.Schema.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *JSONSchemaPropsOrStringArray) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: JSONSchemaPropsOrStringArray: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: JSONSchemaPropsOrStringArray: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Schema", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Schema == nil {
				m.Schema = &JSONSchemaProps{}
			}
			if err := m.Schema.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Property", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Property = append(m.Property, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SelectableField) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SelectableField: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SelectableField: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field JSONPath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.JSONPath = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ServiceReference) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ServiceReference: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ServiceReference: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Namespace", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Namespace = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Path", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Path = &s
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Port", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Port = &v
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ValidationRule) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ValidationRule: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ValidationRule: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rule", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rule = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MessageExpression", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MessageExpression = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := FieldValueErrorReason(dAtA[iNdEx:postIndex])
			m.Reason = &s
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FieldPath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FieldPath = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OptionalOldSelf", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.OptionalOldSelf = &b
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WebhookClientConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WebhookClientConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WebhookClientConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Service", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Service == nil {
				m.Service = &ServiceReference{}
			}
			if err := m.Service.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CABundle", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CABundle = append(m.CABundle[:0], dAtA[iNdEx:postIndex]...)
			if m.CABundle == nil {
				m.CABundle = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field URL", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.URL = &s
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WebhookConversion) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WebhookConversion: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WebhookConversion: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClientConfig", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ClientConfig == nil {
				m.ClientConfig = &WebhookClientConfig{}
			}
			if err := m.ClientConfig.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConversionReviewVersions", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGenerated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGenerated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ConversionReviewVersions = append(m.ConversionReviewVersions, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGenerated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGenerated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipGenerated(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGenerated
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGenerated
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthGenerated
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupGenerated
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthGenerated
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthGenerated        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGenerated          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupGenerated = fmt.Errorf("proto: unexpected end of group")
)
